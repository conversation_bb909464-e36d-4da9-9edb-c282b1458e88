import mockjs from 'mockjs';

const homeFEConstantsConfig = {
    // 页面设置
    frameConfig: {
        themeMap: {
            // 兜底主题，应对key没对应上的情况
            default: {
                // 主题色，状态栏颜色
                themeColor: '#f44f3c',
                // 头部背景遮罩主颜色，第一个颜色需要和主题色一致，不然会有断层
                headerBackMaskBackground: 'linear-gradient(180deg, #f44f3c 0%, #ff7840 20%, #fba169 62%, #0000 100%)',
                // 底部背景遮罩颜色
                footerBackMaskBackground: 'linear-gradient(180deg, #0000 0%, #ffdcbfe6 42%, #ffefd1 100%)',
                backgroundColor: '#FFDCBFE5',
            },
            jinghua: {
                themeColor: '#f44f3c',
                headerBackMaskBackground: 'linear-gradient(180deg, #f44f3c 0%, #ff7840 20%, #fba169 62%, #0000 100%)',
                footerBackMaskBackground: 'linear-gradient(180deg, #0000 0%, #ffdcbfe6 42%, #ffefd1 100%)',
                backgroundColor: '#FFDCBFE5',
            },
            xile: {
                themeColor: '#188BFF',
                headerBackMaskBackground: 'linear-gradient(180deg, #188BFF 0%, #57A7F7 20%, #98CBFF 62%, #0000 100%)',
                footerBackMaskBackground: 'linear-gradient(180deg, #0000 0%, #d4eaffe6 42%, #C7E4FF 100%)',
                backgroundColor: '#D4EAFFE5',
            },
            furao: {
                themeColor: '#BBC527',
                headerBackMaskBackground: 'linear-gradient(180deg, #BBC527 0%, #CFD83E 20%, #E2E796 67%, #0000 100%)',
                footerBackMaskBackground: 'linear-gradient(180deg, #0000 0%, #fdf9c8e6 42%, #EAE497 100%)',
                backgroundColor: '#EAE497',
            },
            fengshou: {
                themeColor: '#FFB126',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #FFB126 -7.01%, #FFBF4E 19.83%, #FFCF7A 67.82%, rgba(255, 207, 122, 0) 99.42%)',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(255, 223, 166, 0) 0.78%, rgba(255, 223, 166, 0.9) 42.63%, #FFDFA6 100%)',
                backgroundColor: '#FFDFA6',
            },
            duocai: {
                themeColor: '#55DA56',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #55DA56 -7.01%, #42D443 19.83%, #85E686 67.82%, rgba(130, 220, 131, 0) 99.42%)',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(204, 241, 169, 0) 0.78%, rgba(204, 241, 169, 0.9) 42.63%, #CCF1A9 100%)',
                backgroundColor: '#CCF1A9',
            },
            jiangnan: {
                themeColor: '#BBFFF4',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #20C1C3 -7.01%, #08D5C4 19.83%, #08D5C4 67.82%, rgba(8, 213, 196, 0) 99.42%)',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(187, 255, 244, 0) 0.78%, rgba(187, 255, 244, 0.9) 42.63%, #BBFFF4 100%)',
                backgroundColor: '#BBFFF4',
            },
            yangfan: {
                themeColor: '#00CBFC',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #00CBFC -7.01%, #00CBFC 19.83%, #00CBFC 67.82%, rgba(0, 203, 252, 0) 99.42%)',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(142, 241, 255, 0) 0.78%, rgba(142, 241, 255, 0.9) 42.63%, #8EF1FF 100%)',
                backgroundColor: '#8EF1FF',
            },
        },
    },
    // 花字提示
    flowerTip: {
        signed: '打卡成功',
        day2: '送你${0}步 冲!',
        day100: '最后一步 冲!',
    },
    guideTitle: '连续打卡100天 赢好礼',
    // 主按钮文案
    mainButtonConfig: {
        leftSideButtonText: '赚现金',
        rightSideButtonText: '赚步数',
        noStepToast: '没有步数可以向前冲啦，明天再来吧',
        // 断签
        SIGN_INTERCEPTED: {
            buttonText: '立即补签',
            buttonSubText: '后不可补签',
            bubbleText: '你已断签啦',
            bubbleSubText: '快来拯救进度',
        },
        // 已签到
        SIGNED: {
            buttonText: '今日已打卡',
            newButtonText: '向前冲',
            buttonSubText: '剩余步数: ',
            duration: 3000, // 气泡展示的时长
            bubbleText: '今日打卡过了',
            bubbleSubText: '明天再来哦',
        },
        // 免签
        SIGN_FREE: {
            buttonText: '使用免任务卡',
            buttonSubText: '剩余步数: ',
            bubbleText: '一键使用!',
            bubbleSubText: '直接完成今日打卡',
        },
        // 未签到-有次数
        UN_SIGN: {
            buttonText: '向前冲',
            buttonSubText: '剩余步数: ',
            bubbleText: '再冲{0}完成打卡!',
        },
        // 首日
        FIRST_DAY: {
            buttonText: '向前冲',
            buttonSubText: '剩余步数: ',
            bubbleText: '打卡100天赢手机',
            bubbleSubText: '立即冲',
        },
        OTHER: {
            buttonText: '重新挑战',
        },
    },
    // 建筑循环配置
    buildingInterval: 8,
    stationTagView: {
        noSigned: {
            first: '今日待打卡',
            second: '第{0}天',
            another: '第{0}天',
        },
        signed: {
            first: '第{0}天已打卡',
            second: '明日待打卡',
            another: '第{0}天',
        },
    },
    // 桌面图标配置
    shortcutConfig: {
        name: '百日心愿',
        id: 'summer25',
        bgColor: '#F2FCFF',
        btText: '打开活动页',
    },
    weakGuideConfig: {
        /** 站点奖励气泡 */
        GUIDE_REWARD: {},
        /** 引导赚步数 */
        GUIDE_RIGHT_BUTTON: {
            // did 纬度频控
            totalLimitCount: 30,
            // 消失时间
            // duration: 5000,
        },
        /** 引导组队 */
        GUIDE_TEAM: {
            // 每日频控
            everydayLimitCount: 100,
            // 消失时间
            duration: 10000,
        },
        /** 引导组队打卡 */
        GUIDE_CARD: {
            // 每日频控
            everydayLimitCount: 5,
            // 消失时间
            duration: 10000,
        },
    },
    // 挽留弹窗频控
    retainFrequency: {
        // 打卡挽留弹窗
        signRetainPopup: {
            dayTime: 3,
            allTime: 9999, // 无全周期频控
        },
        // 快捷方式挽留弹窗
        shortcutRetainPopup: {
            dayTime: 3,
            allTime: 99,
        },
        // 搜索挽留弹窗
        searchRetainPopup: {
            dayTime: 3,
            allTime: 99,
        },
    },
    inpushConfig: {
        // 兜底轮询间隔
        interval: 60000,
    },
    replaceProductConfirmText: {
        FIRST: '连续打卡100天必得',
        NORMAL: '更换后挑战进度将会清零哦测试',
        TEAM: '你还在队伍中，先退队吧！',
    },
    broadcastTag: {
        INTERCEPT: '打卡中断！',
        UN_SIGN: '今日待打卡',
        SIGNED: '今日已打卡',
    },
    broadcastText: {
        INTERCEPT: '最后通牒，即将痛失好礼！',
        DEFAULT: '查看我的打卡成就',
    },
    progressPointText: {
        INTERCEPT: '中断',
        DEFAULT: '第{0}天',
        TODAY: '今天',
        TARGET: '{0}天必得',
    },
    rewardTransPopupText: {
        title: '挑战成功',
        subTitle: '百天成就达成，速速发朋友圈收割膝盖',
    },
    renderGroup: 2,
    progressViewConf: {
        topText: {
            default: '今日待打卡 快向前冲吧',
            signed: '已打卡{0}天 明日继续',
            intercept: '打卡已中断！快去补打卡吧',
        },
    },
};

const homeUEConstantsConfig = {
    activityUrl: 'https://summer25.staging.kuaishou.com/home?layoutType=4',
    // searchContentIcon:
    //     'https://kcdn-w1.staging.kuaishou.com/kc/files/a/cny2025-server/warmup/home/<USER>/searchContentIcon.png',
    // mainTitleImg:
    //     'https://kcdn-w1.staging.kuaishou.com/kc/files/a/cny2025-server/warmup/home/<USER>/main-title.0b522789a6414813.png',

    // 活动规则页链接
    activityRuleUrl: 'https://baidu.com',
    // 钱包页链接
    walletUrl:
        'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=12749&themeStyle=1&entry_src=ks_cny_089',
    // 客服页链接
    customerServiceUrl:
        'https://csc-center.staging.kuaishou.com/help/index.html?enableWK=1&layoutType=4#/?entranceId=5579&environment=staging',

    // 桌面图标图片
    shortcutIcon: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/retain-popup/icon.png',
    // 活动链接，桌面图标跳转需要
    shortcutUrl: 'https://summer25.staging.kuaishou.com/home?layoutType=4',
    // 添加桌面图标弹窗里面展示的图片
    shortcutContentIcon: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/retain-popup/contentIcon.png',
    // ios快捷方式中间页背景
    shortcutBgImg:
        'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/retain-popup/ios-shortcut-proxy-back.png',

    // 分享打卡天数
    shareNumber0: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num0.png',
    shareNumber1: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num1.png',
    shareNumber2: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num2.png',
    shareNumber3: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num3.png',
    shareNumber4: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num4.png',
    shareNumber5: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num5.png',
    shareNumber6: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num6.png',
    shareNumber7: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num7.png',
    shareNumber8: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num8.png',
    shareNumber9: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/num9.png',

    /** 和主题对应的分享海报，但是还不确定主题有多少个，主题名等 */

    /** 分享海报，兜底图 */
    sharePoster_default: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/sharePoster_default.png',
    /** 分享海报，对应 jinghua */
    sharePoster_jinghua: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/sharePoster_jinghua.png',
    /** 分享海报，对应 xile */
    sharePoster_xile: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/sharePoster_xile.png',
    /** 分享海报，对应 furao */
    sharePoster_furao: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/sharePoster_furao.png',
};

const mockTeamExitPopup = {
    popupType: 'TEAM_EXIT',
    subTitle: '队伍解散',
    message: '好友断签，队伍解散\n别担心，你的个人打卡天数不受影响！',
    mainButton: '我知道了',
    toast: '队友退队',
    type: 1,
    reasonType: 1,
};

const mockTeamSuccessPopup = {
    popupType: 'TEAM_SUCCESS',
    animationTitle: '组队成功',
    animationDesc: '全员连续打卡7天得直通卡 1人断签即失败',
    icon: 'https://ali.a.yximgs.com/kos/nlav12119/jTyADRou_2025-05-07-15-43-46.png',
    mainButton: {
        linkType: 'GO_TEAM',
        linkText: '开启组队打卡',
    },
};

const mockReceiveTaskFreeCardPopup = {
    popupType: 'TEAM_SIGN_REWARD',
    title: '恭喜获得7张直通卡',
    desc: '每天无需任务，点击即可打卡',
    icon: 'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/team/freeCardImg.png?x-kcdn-pid=112543',
    mainButton: {
        linkType: 'CLOSE',
        linkText: '开心收下',
    },
};

const getCurrentStep = () => {
    const data = require('../../chess/move/data.json');
    return data.currentStep;
};

// chessboard数据格式感觉有点乱
// 已删除无用字段
const genChessboard = () => {
    return {
        progress: {
            currentStep: getCurrentStep(),
            expectTotalStep: 10,
            signed: getCurrentStep() >= 10 ? true : false,
            lastStation: false,
        },
        buildingInfos: [
            {
                type: 'decration',
                name: 'normal',
                iconUrl:
                    'https://kcdn.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/signIn/stationIcon/tianjin.png?x-kcdn-pid=112543',
                title: 'string',
                desc: 'string',
                linkUrl: 'https://kcdn-w1.staging.kuaishou.com/string?x-kcdn-pid=112543',
                pos: [5],
            },
        ],
        stationHotInfoViews: {
            stationDesc: '新鲜事儿',
            hotInfos: [
                {
                    title: '你好。。。。',
                    linkUrl:
                        'kwai://home/<USER>',
                },
            ],
            liveTime: 102000,
        },
        userInfo: {
            userName: 'User_1617365252233',
            headUrl:
                'https://kcdn.staging.kuaishou.com/uhead/AB/2025/03/10/18/BMjAyNTAzMTAxODIzMjRfMjE5ODA4MTcwMl8xX2hkMjIxXzY5Nw==_s.jpg?x-kcdn-pid=112543',
        },
        stationList: [
            {
                stationInfo: {
                    uniqueKey: 'beijing',
                    stationName: '北京',
                    stationIcon:
                        'https://kcdn-w1.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/chessboard/station/beijing.png',
                    llrewdIcon:
                        'https://kcdn-w1.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/signIn/stationGold.png',
                    stationThemeKey: 'jinghua',
                    signed: false,
                    stationDayIndex: 1,
                    stationTotalStep: 1,
                    stationBubbleIcon: null,
                    stationBubbleText: '今日打卡/n最高得{1888金币}',
                    bubbleShowSeconds: 500,
                    tomorrowBubbleText: '明日打卡/n最高得{1888金币}',
                    gridSkinUrl:
                        'https://kcdn-w1.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/chessboard/grid.png?x-kcdn-pid=112543',
                },
                llrewdGridLayout: {
                    LLCH_GRID: {
                        gridIcon:
                            'https://kcdn-w1.staging.kuaishou.com/kcdn/cdn-kcdn112307/home/<USER>',
                        gridLocation: [4],
                    },
                    LLCN_GRID: {
                        gridIcon: 'https://kcdn.staging.kuaishou.com/kcdn/cdn-kcdn112307/home/<USER>',
                        gridLocation: [1, 3],
                    },
                },
            },
            {
                stationInfo: {
                    uniqueKey: 'tianjin',
                    stationName: '天津',
                    stationIcon:
                        'https://kcdn-w1.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/signIn/stationIcon/tianjindark.png',
                    llrewdIcon:
                        'https://kcdn-w1.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/signIn/stationBlack.png',
                    stationThemeKey: 'jinghua',
                    signed: false,
                    stationDayIndex: 2,
                    stationTotalStep: 3,
                    stationBubbleIcon: null,
                    stationBubbleText: '今日打卡/n最高得{1888金币}',
                    bubbleShowSeconds: 1000,
                    tomorrowBubbleText: '明日打卡/n最高得{1888金币}',
                    gridSkinUrl: null,
                },
                llrewdGridLayout: {
                    LLCH_GRID: {
                        gridIcon:
                            'https://kcdn-w1.staging.kuaishou.com/kcdn/cdn-kcdn112307/home/<USER>',
                        gridLocation: [4],
                    },
                    LLCN_GRID: {
                        gridIcon: 'https://kcdn.staging.kuaishou.com/kcdn/cdn-kcdn112307/home/<USER>',
                        gridLocation: [1],
                    },
                },
            },
            {
                stationInfo: {
                    uniqueKey: 'shijiazhuang',
                    stationName: '石家庄',
                    stationIcon:
                        'https://kcdn-w1.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/signIn/stationIcon/shijiazhuangdark.png',
                    llrewdIcon:
                        'https://kcdn-w1.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/signIn/stationGold.png',
                    stationThemeKey: 'jinghua',
                    signed: false,
                    stationDayIndex: 3,
                    stationTotalStep: 3,
                    stationBubbleIcon: null,
                    stationBubbleText: '今日打卡/n最高得{1888金币}',
                    bubbleShowSeconds: 500,
                    tomorrowBubbleText: '明日打卡/n最高得{1888金币}',
                    gridSkinUrl: null,
                },
                llrewdGridLayout: {
                    LLCH_GRID: {
                        gridIcon: 'https://kcdn.staging.kuaishou.com/kcdn/cdn-kcdn112307/home/<USER>',
                        gridLocation: [],
                    },
                    LLCN_GRID: {
                        gridIcon:
                            'https://kcdn-w1.staging.kuaishou.com/kcdn/cdn-kcdn112307/home/<USER>',
                        gridLocation: [],
                    },
                },
            },
            {
                stationInfo: {
                    uniqueKey: 'baoding',
                    stationName: '保定',
                    stationIcon:
                        'https://kcdn-w1.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/signIn/stationIcon/baodingdark.png',
                    llrewdIcon: '',
                    stationThemeKey: 'jinghua',
                    signed: false,
                    stationDayIndex: 4,
                    stationTotalStep: 3,
                    stationBubbleIcon: null,
                    stationBubbleText: '今日打卡/n最高得{1888金币}',
                    bubbleShowSeconds: 500,
                    tomorrowBubbleText: '明日打卡/n最高得{1888金币}',
                    gridSkinUrl: null,
                },
                llrewdGridLayout: {
                    LLCH_GRID: {
                        gridIcon:
                            'https://kcdn-w1.staging.kuaishou.com/kcdn/cdn-kcdn112307/home/<USER>',
                        gridLocation: [],
                    },
                    LLCN_GRID: {
                        gridIcon: 'https://kcdn.staging.kuaishou.com/kcdn/cdn-kcdn112307/home/<USER>',
                        gridLocation: [],
                    },
                },
            },
            {
                stationInfo: {
                    uniqueKey: 'handan',
                    stationName: '邯郸',
                    stationIcon:
                        'https://kcdn-w1.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/signIn/stationIcon/handandark.png',
                    llrewdIcon: '',
                    stationThemeKey: 'jinghua',
                    signed: false,
                    stationDayIndex: 5,
                    stationTotalStep: 3,
                    stationBubbleIcon: null,
                    stationBubbleText: '今日打卡/n最高得{1888金币}',
                    bubbleShowSeconds: 500,
                    tomorrowBubbleText: '明日打卡/n最高得{1888金币}',
                    gridSkinUrl: null,
                },
                llrewdGridLayout: {
                    LLCH_GRID: {
                        gridIcon:
                            'https://kcdn-w1.staging.kuaishou.com/kcdn/cdn-kcdn112307/home/<USER>',
                        gridLocation: [],
                    },
                    LLCN_GRID: {
                        gridIcon:
                            'https://kcdn-w1.staging.kuaishou.com/kcdn/cdn-kcdn112307/home/<USER>',
                        gridLocation: [],
                    },
                },
            },
        ],
        currentStationIndex: 0,
    };
};

const gen = () => ({
    result: 1,
    data: {
        signInHomeView: {
            progressArea: {
                stationInfos: [
                    {
                        uniqueKey: 'beijing',
                        stationName: '北京',
                        stationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/city/beijing.png',
                        chessStationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/station/beijing.png?x-kcdn-pid=112543',
                        llrewdIcon: '',
                        stationThemeKey: 'jinghua',
                        signed: false,
                        stationDayIndex: 1,
                        stationTotalStep: 1,
                        stationBubbleIcon: null,
                        stationBubbleText: '',
                        tomorrowBubbleText: null,
                        bubbleShowSeconds: 0,
                        gridSkinUrl:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/skin/skin.png?x-kcdn-pid=112543',
                    },
                    {
                        uniqueKey: 'tianjin',
                        stationName: '天津',
                        stationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/city/tianjindark.png',
                        chessStationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/station/tianjian.png?x-kcdn-pid=112543',
                        llrewdIcon:
                            'https://kcdn-w1.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/signIn/stationBlack.png',
                        stationThemeKey: 'xile',
                        signed: false,
                        stationDayIndex: 2,
                        stationTotalStep: 5,
                        stationBubbleIcon:
                            'https://p4-plat.wsukwai.com/kc/files/a/summer2025-server/wishTravel/signIn/stationBlack.png?x-kcdn-pid=112543',
                        stationBubbleText: '明日打卡/n最高得{1888金币}',
                        tomorrowBubbleText: null,
                        bubbleShowSeconds: 0,
                        gridSkinUrl:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/skin/skin.png?x-kcdn-pid=112543',
                    },
                    {
                        uniqueKey: 'shijiazhuang',
                        stationName: '石家庄',
                        stationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/city/shijiazhuangdark.png',
                        chessStationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/station/shijiazhuang.png?x-kcdn-pid=112543',
                        llrewdIcon:
                            'https://kcdn-w1.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/signIn/stationGold.png',
                        stationThemeKey: 'jinghua',
                        signed: false,
                        stationDayIndex: 3,
                        stationTotalStep: 6,
                        stationBubbleIcon:
                            'https://p4-plat.wsukwai.com/kc/files/a/summer2025-server/wishTravel/signIn/stationGold.png?x-kcdn-pid=112543',
                        stationBubbleText: '今日打卡/n最高得{1888金币}',
                        tomorrowBubbleText: null,
                        bubbleShowSeconds: 0,
                        gridSkinUrl:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/skin/skin.png?x-kcdn-pid=112543',
                    },
                    {
                        uniqueKey: 'handan',
                        stationName: '邯郸',
                        stationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/city/handandark.png',
                        chessStationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/station/handan.png?x-kcdn-pid=112543',
                        llrewdIcon:
                            'https://kcdn-w1.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/signIn/stationCoupon.png',
                        stationThemeKey: 'jinghua',
                        signed: false,
                        stationDayIndex: 4,
                        stationTotalStep: 7,
                        stationBubbleIcon:
                            'https://p4-plat.wsukwai.com/kc/files/a/summer2025-server/wishTravel/signIn/stationCoupon.png?x-kcdn-pid=112543',
                        stationBubbleText: '',
                        tomorrowBubbleText: null,
                        bubbleShowSeconds: 0,
                        gridSkinUrl:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/skin/skin.png?x-kcdn-pid=112543',
                        newLlrewdIcon: 'https://ali.a.yximgs.com/kos/nlav12119/fNOQfGmy_2025-06-20-21-04-17.png',
                        stationIconRewardTextColor: '#00D164',
                        stationIconRewardText: '9999',
                    },
                ],
                broadCastInfo: {
                    todaySignIn: true,
                    nextLLrewdSignInDays: 1,
                    llrewdName: '神秘奖励',
                },
                todayIndex: 0,
                todayStationInfo: {
                    uniqueKey: 'beijing',
                    stationName: '北京',
                    stationIcon: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/city/beijing.png',
                    chessStationIcon:
                        'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/station/beijing.png?x-kcdn-pid=112543',
                    llrewdIcon: '',
                    stationThemeKey: 'jinghua',
                    signed: true,
                    stationDayIndex: 1,
                    stationTotalStep: 1,
                    stationBubbleIcon: null,
                    stationBubbleText: '',
                    tomorrowBubbleText: null,
                    bubbleShowSeconds: 0,
                    gridSkinUrl:
                        'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/skin/skin.png?x-kcdn-pid=112543',
                },
                needSignInDays: 10,
            },
            product: {
                productId: 1001,
                productName: '电商测试商品2',
                productIcon: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/product/iphone16.png',
                signInDays: 10,
            },
            eventId: '107#108',
            calendarEventViewList: [
                {
                    title: '心愿大巴签到提醒',
                    note: '打开快手进入活动页面',
                    url: 'https://m.ssl.kuaishou.com/app/webview?url=https%3A%2F%2Fsummer25.staging.kuaishou.com%3FlayoutType%3D4%26entry_src%3Dcalendar',
                    startDay: 1747785600000, //2025-05-21 08:00:00
                    endDay: 1748620800000, //2025-05-31 00:00:00
                    type: 1,
                },
                {
                    title: '快去确认今日心愿打卡已完成，错过将与大奖失之交臂',
                    note: '打开快手进入活动页面',
                    url: null,
                    startDay: 1747825200000, // 2025-05-21 19:00:00
                    endDay: 1748620800000, //2025-05-31 00:00:00
                    type: 1,
                },
            ],
            signInDays: 0,
        },
        todaySigned: false,
        chessStepSwitch: true,
        signInStatus: 'PROCESSING',
        chessboard: {
            progress: {
                currentStep: getCurrentStep(),
                expectTotalStep: 10,
                signed: false,
                lastStation: false,
            },
            buildingInfos: [
                {
                    type: 'guide',
                    name: 'bussiness',
                    iconUrl:
                        'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/building/building.png?x-kcdn-pid=112543',
                    title: 'string',
                    desc: 'string',
                    linkUrl: 'https://summer25.staging.kuaishou.com/home?layoutType=4&_vconsole=1',
                    linkType: 'JUMP_H5',
                    pos: [4],
                },
            ],
            stationHotInfoViews: {
                stationDesc: '北京新鲜事',
                hotInfos: [
                    {
                        title: '你好',
                        linkUrl: 'ksnebula://home/<USER>',
                    },
                    {
                        title: '你真好',
                        linkUrl: 'ksnebula://home/<USER>',
                    },
                ],
                liveTime: 102000,
            },
            userInfo: {
                userName: '快手用户1747819956470',
                headUrl: 'https://kcdn-w1.staging.kuaishou.com/BMjAyNTA2MjMxNDU1MTBfMjE5ODg0MTU0Ml8yX2hkODU1XzUw_s.jpg',
            },
            stationList: [
                {
                    stationInfo: {
                        uniqueKey: 'xian',
                        stationName: '北京',
                        stationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/city/beijing.png',
                        chessStationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/station/xian.png',
                        llrewdIcon: '',
                        stationThemeKey: 'fengshou',
                        signed: true,
                        stationDayIndex: 1,
                        stationTotalStep: 1,
                        stationBubbleIcon: null,
                        stationBubbleText: '',
                        tomorrowBubbleText: null,
                        bubbleShowSeconds: 0,
                        gridSkinUrl:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/skin/skin.png?x-kcdn-pid=112543',
                        gridSkinLocation: [3],
                    },
                    llrewdGridLayout: {
                        LLCH_GRID: {
                            gridIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCH.png?x-kcdn-pid=112543',
                            gridLocation: [],
                        },
                        LLCN_GRID: {
                            gridIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCN.png?x-kcdn-pid=112543',
                            gridLocation: [],
                        },
                    },
                },
                {
                    stationInfo: {
                        uniqueKey: 'tianjin',
                        stationName: '天津',
                        stationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/city/tianjindark.png',
                        chessStationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/station/tianjian.png?x-kcdn-pid=112543',
                        llrewdIcon:
                            'https://kcdn-w1.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/signIn/stationBlack.png',
                        stationThemeKey: 'xile',
                        signed: false,
                        stationDayIndex: 2,
                        stationTotalStep: 5,
                        stationBubbleIcon:
                            'https://p4-plat.wsukwai.com/kc/files/a/summer2025-server/wishTravel/signIn/stationBlack.png?x-kcdn-pid=112543',
                        stationBubbleText: '明日打卡/n最高得{1888金币}',
                        tomorrowBubbleText: null,
                        bubbleShowSeconds: 0,
                        gridSkinUrl:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/skin/skin.png?x-kcdn-pid=112543',
                        gridSkinLocation: [3],
                    },
                    llrewdGridLayout: {
                        LLCH_GRID: {
                            gridIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCH.png?x-kcdn-pid=112543',
                            gridLocation: [],
                        },
                        LLCN_GRID: {
                            gridIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCN.png?x-kcdn-pid=112543',
                            gridLocation: [2, 4],
                        },
                    },
                },
                {
                    stationInfo: {
                        uniqueKey: 'haerbin',
                        stationName: '石家庄',
                        stationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/city/shijiazhuangdark.png',
                        chessStationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/station/haerbin.png',
                        llrewdIcon:
                            'https://kcdn-w1.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/signIn/stationGold.png',
                        stationThemeKey: 'jinghua',
                        signed: false,
                        stationDayIndex: 3,
                        stationTotalStep: 2,
                        stationBubbleIcon:
                            'https://p4-plat.wsukwai.com/kc/files/a/summer2025-server/wishTravel/signIn/stationGold.png?x-kcdn-pid=112543',
                        stationBubbleText: '今日打卡/n最高得{1888金币}',
                        tomorrowBubbleText: null,
                        bubbleShowSeconds: 0,
                        gridSkinUrl:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/skin/skin.png?x-kcdn-pid=112543',
                        gridSkinLocation: [3],
                    },
                    llrewdGridLayout: {
                        LLCH_GRID: {
                            gridIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCH.png?x-kcdn-pid=112543',
                            gridLocation: [2],
                        },
                        LLCN_GRID: {
                            gridIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCN.png?x-kcdn-pid=112543',
                            gridLocation: [3, 51],
                        },
                    },
                },
                {
                    stationInfo: {
                        uniqueKey: 'qiqihaer',
                        stationName: '齐齐哈尔',
                        stationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/city/baodingdark.png',
                        chessStationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/station/baoding.png?x-kcdn-pid=112543',
                        llrewdIcon: '',
                        stationThemeKey: 'jinghua',
                        signed: false,
                        stationDayIndex: 4,
                        stationTotalStep: 6,
                        stationBubbleIcon: null,
                        stationBubbleText: '',
                        tomorrowBubbleText: null,
                        bubbleShowSeconds: 0,
                        gridSkinUrl: null,
                        gridSkinLocation: [3],
                    },
                    llrewdGridLayout: {
                        LLCH_GRID: {
                            gridIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCH.png?x-kcdn-pid=112543',
                            gridLocation: [],
                        },
                        LLCN_GRID: {
                            gridIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCN.png?x-kcdn-pid=112543',
                            gridLocation: [1, 3],
                        },
                    },
                },
                {
                    stationInfo: {
                        uniqueKey: 'handan',
                        stationName: '邯郸',
                        stationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/city/handandark.png',
                        chessStationIcon:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/station/handan.png?x-kcdn-pid=112543',
                        llrewdIcon:
                            'https://kcdn-w1.staging.kuaishou.com/kc/files/a/summer2025-server/wishTravel/signIn/stationCoupon.png',
                        stationThemeKey: 'jinghua',
                        signed: false,
                        stationDayIndex: 5,
                        stationTotalStep: 7,
                        stationBubbleIcon:
                            'https://p4-plat.wsukwai.com/kc/files/a/summer2025-server/wishTravel/signIn/stationCoupon.png?x-kcdn-pid=112543',
                        stationBubbleText: '',
                        tomorrowBubbleText: null,
                        bubbleShowSeconds: 0,
                        gridSkinUrl:
                            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/skin/skin.png?x-kcdn-pid=112543',
                        gridSkinLocation: [3],
                    },
                    llrewdGridLayout: {
                        LLCH_GRID: {
                            gridIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCH.png?x-kcdn-pid=112543',
                            gridLocation: [],
                        },
                        LLCN_GRID: {
                            gridIcon:
                                'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/chessboard/llwd/LLCN.png?x-kcdn-pid=112543',
                            gridLocation: [3, 5],
                        },
                    },
                },
            ],
            currentStationIndex: 0,
            initStationCount: 5,
        },
        mainButton: {
            rushCount: 0,
            reSignInCountdownTime: 0,
            stationDayIndex: 1,
            hasValidSignFreeCard: false,
            systemGivingRushChance: 0,
        },
        abTestConfigView: {
            beginnerGuideStrategy: 4,
        },
        titleInfo: {
            logo: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/home/<USER>',
            title: '百天心愿之旅',
            subTitle: '打卡10天必得终点豪礼',
        },
        accountModel: {
            total: '0.00',
            unit: '元',
        },
        homeUEConstantsConfig,
        homeFEConstantsConfig,
        homeResourceMap: {
            RESOURCE_LEFT_BOTTOM: {
                id: 0,
                labelDesc: '活动B',
                title: '活动B',
                icon: null,
                iconText: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/home/<USER>',
                linkType: 'JUMP_H5',
                linkUrl:
                    'https://sf2025.m.kuaishou.cn/warmup/cards?layoutType=4&bizId=25cny-warmup&entry_src=ks_cny_082&softInputMode=50&pad_h5_promote_disable=true',
            },
            RESOURCE_RIGHT_BOTTOM: {
                id: 0,
                labelDesc: '集福卡',
                title: '集福卡',
                icon: null,
                iconText: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/home/<USER>',
                linkType: 'JUMP_H5',
                linkUrl:
                    'https://sf2025.m.kuaishou.cn/warmup/cards?layoutType=4&bizId=25cny-warmup&entry_src=ks_cny_082&softInputMode=50&pad_h5_promote_disable=true',
            },
            RESOURCE_RIGHT_TOP: {
                id: 0,
                labelDesc: '活动D',
                title: '活动D',
                icon: null,
                iconText: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/home/<USER>',
                linkType: 'JUMP_H5',
                linkUrl:
                    'https://sf2025.m.kuaishou.cn/warmup/cards?layoutType=4&bizId=25cny-warmup&entry_src=ks_cny_082&softInputMode=50&pad_h5_promote_disable=true',
            },
            RESOURCE_LEFT_TOP: {
                id: 0,
                labelDesc: '活动A',
                title: '活动A',
                icon: null,
                iconText: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/home/<USER>',
                linkType: 'JUMP_H5',
                linkUrl:
                    'https://sf2025.m.kuaishou.cn/warmup/cards?layoutType=4&bizId=25cny-warmup&entry_src=ks_cny_082&softInputMode=50&pad_h5_promote_disable=true',
            },
        },
        popList: [
            // {
            //     sponsorLogo: null,
            //     sponsorText: null,
            //     popupType: 'BEGINNER_GUIDE',
            //     title: null,
            //     subTitle: '天降福利 玩法升级',
            //     userId: 2198474502,
            //     activityId: 'summer25Main_t2',
            //     mainButton: {
            //         linkType: 'CLOSE',
            //         linkText: '我知道了',
            //         linkSubText: null,
            //         linkUrl: null,
            //         icon: null,
            //     },
            //     coinPopup: true,
            //     subButton: null,
            //     taskId: 0,
            //     sortScore: 0,
            //     hashKeys: [],
            //     desc: '无需步数即可向前冲，快去试试吧～',
            //     icon: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/olduser/oldUserIcon.png',
            // },
            // {
            //     sponsorLogo: null,
            //     sponsorText: null,
            //     popupType: 'FINAL_RECEIVE_EXPIRE',
            //     title: null,
            //     subTitle: '天降福利 玩法升级',
            //     userId: 2198474502,
            //     activityId: 'summer25Main_t2',
            //     mainButton: {
            //         linkType: 'CLOSE',
            //         linkText: '我知道了',
            //         linkSubText: null,
            //         linkUrl: null,
            //         icon: null,
            //     },
            //     subButton: null,
            //     taskId: 0,
            //     sortScore: 0,
            //     hashKeys: [],
            //     desc: '无需步数即可向前冲，快去试试吧～',
            //     icon: 'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/olduser/oldUserIcon.png',
            // },
            // {
            //     popupType: 'HUGE_SIGN_IN_RESUME',
            //     title: '可任选一方式完成续签',
            //     subTitle: '04月05日未打卡',
            //     financeMethod: {
            //         title: '使用70.19元现金+1.28活动余额',
            //         subTitle: '现金余额:2.5元 金币余额:158',
            //         cornerText: '1.使用现金余额及金币续签',
            //         buttonText: '确认使用',
            //         hasBadgeIcon: false,
            //         resumeType: 2,
            //         continuePay: false,
            //         cashAmount: 19,
            //         coinAmount: 100,
            //     },
            //     shareAssistMethod: {
            //         title: '邀请2位好友(0/2)',
            //         subTitle: '好友每天仅可助力一次',
            //         cornerText: '2.邀人免费续签',
            //         buttonText: '去邀请',
            //         hasBadgeIcon: true,
            //         subBiz: 'NEBULA_SIGNIN',
            //         hugeSignInShareToken: 'NnO2iAs02K_mNR8Dzv85hUmvaDw2haqH',
            //     },
            // },
            // {
            //     sponsorLogo: null,
            //     sponsorText: null,
            //     popupType: 'FINAL_RECEIVE_EXPIRE',
            //     title: '很遗憾 挑战失败',
            //     subTitle: '断签天数超过5天',
            //     userId: 0,
            //     activityId: null,
            //     mainButton: {
            //         linkType: 'NEW_ROUND_SIGN',
            //         linkText: '重新挑战',
            //         linkSubText: null,
            //         linkUrl: null,
            //         icon: null,
            //     },
            //     subButton: null,
            //     taskId: 0,
            //     sortScore: 0,
            //     hashKeys: [],
            //     desc: null,
            //     icon: null,
            // },
        ],
        needSig3Path: [
            '/rest/wd/summer25/wishTravel/hugeSignIn/selectProduct',
            '/rest/wd/summer25/wishTravel/hugeSignIn/finalLlrewd',
            '/rest/wd/summer25/wishTravel/hugeSignIn/resume',
            '/rest/wd/summer25/wishTravel/hugeSignIn/abandon',
            '/rest/wd/summer25/wishTravel/hugeSignIn/pay/cancel',
            '/rest/wd/summer25/wishTravel/llwdwc/sign',
            '/rest/n/summer2025/wish/travel/llwdwc/sign',
            '/rest/nebula/summer2025/wish/travel/llwdwc/sign',
        ],
        degradeConfigView: {
            cdnLevel: 3,
            animationLevel: 0,
            videoLevel: 0,
            liveLevel: 0,
            audioLevel: 0,
            transparentVideoLevel: 2,
        },
        teamEntryView: {
            entryStatus: 1,
            newEntryStatus: 1,
            freeCardImg:
                'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/team/freeCardImg.png?x-kcdn-pid=112543',
            teamUser: [
                {
                    userId: 2198854870,
                    nickName: '快手用户1747819956470',
                    userAvatar: 'https://p4-plat.wskwai.com/kos/nlav12689/head.jpg',
                    todaySign: false,
                    currentUser: true,
                },
            ],
            showPop: true,
            popType: 1,
            number: 7,
            freeCardListTitle: '直通卡',
            freeCardListSubTitle: '使用直通卡可以直接打卡哦',
            freeCardList: [
                {
                    cardName: '直通卡',
                    cardImg:
                        'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/team/freeCardImg.png?x-kcdn-pid=112543',
                    validTimeStr: '05.30 00:00:00-05.30 23:59:59',
                    buttonType: 1,
                },
                {
                    cardName: '直通卡',
                    cardImg:
                        'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/team/freeCardImg.png?x-kcdn-pid=112543',
                    validTimeStr: '05.30 00:00:00-05.30 23:59:59',
                    buttonType: 2,
                },
                {
                    cardName: '直通卡',
                    cardImg:
                        'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/team/freeCardImg.png?x-kcdn-pid=112543',
                    validTimeStr: '05.30 00:00:00-05.30 23:59:59',
                    buttonType: 3,
                },
                {
                    cardName: '直通卡',
                    cardImg:
                        'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/team/freeCardImg.png?x-kcdn-pid=112543',
                    validTimeStr: '05.30 00:00:00-05.30 23:59:59',
                    buttonType: 4,
                },
                {
                    cardName: '直通卡',
                    cardImg:
                        'https://p4-plat.wskwai.com/kcdn/cdn-kcdn112543/wishTravel/team/freeCardImg.png?x-kcdn-pid=112543',
                    validTimeStr: '05.30 00:00:00-05.30 23:59:59',
                    buttonType: 5,
                },
            ],
        },
        refreshTime: 15000,
        chessStepSwitch: true,
    },
    timestamp: 1747826519256,
    hostname: 'public-xm-c34-kce-node-staging37.idchb1az1.hb1.kwaidc.com',
});

// 注释掉下面这行代码，则会禁用当前接口的 mock
// export const disable = true;
export default () => mockjs.mock(gen);
