{"openapi": "3.0.1", "info": {"title": "API Docs for kuaishou-summer2025-wish-travel-api", "x-ks-git": "*************************:activity/summer2025/user_growth/kuaishou-summer2025-wish-travel.git", "x-ks-module": "kuaishou-summer2025-wish-travel-api", "x-ks-branch": "master", "x-ks-commit-id": "8b051e3d", "x-ks-commit-time": "2025-07-01 19:52:37", "x-ks-commit-message": "Merge branch 'feature_gird_task_fix' into 'master'", "x-ks-swagger-ui": "https://kdev.corp.kuaishou.com/web/api-mock/http/apimgr?projectId=99765&repo=activity/summer2025/user_growth/kuaishou-summer2025-wish-travel&module=kuaishou-summer2025-wish-travel-api&branch=master", "x-ks-api-docs": "https://kdev.corp.kuaishou.com/api/mock/manage/apiManage/openapi/docs?projectId=99765&repo=activity/summer2025/user_growth/kuaishou-summer2025-wish-travel&module=kuaishou-summer2025-wish-travel-api&branch=master&swaggerui=0"}, "tags": [{"name": "homeController", "description": "预热活动首页"}, {"name": "PropsController", "description": "提现卡接口"}, {"name": "HugeSignInController", "description": "25暑期签到"}, {"name": "TaskController", "description": "任务"}, {"name": "ChessController", "description": "棋盘相关接口"}, {"name": "PollingController", "description": "触达 owner:chen<PERSON>11"}, {"name": "homeShareController", "description": "活动分享"}, {"name": "Summer2025WishTravelTeamController", "description": "25暑期组队"}], "paths": {"/rest/wd/summer25/wishTravel/team/exit": {"post": {"tags": ["Summer2025WishTravelTeamController"], "summary": "退出队伍", "operationId": "summer2025WishTravelTeamControllerExitTeam", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewObject"}}}}}}}, "/rest/wd/summer25/wishTravel/share/backFlow": {"post": {"tags": ["homeShareController"], "summary": "分享回流", "operationId": "shareControllerBackFlow", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SummerBackFlowParam"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewSummerBackFlowView"}}}}}}}, "/rest/wd/summer25/wishTravel/llwdwc/sign": {"post": {"tags": ["PropsController"], "summary": "提现卡签名", "description": "用户使用提现卡跳转支付H5时，请求该接口拿到跳转链接", "operationId": "propsControllerSign", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewWishTravelLLwdwCardView"}}}}}}}, "/rest/wd/summer25/wishTravel/hugeSignIn/selectProduct": {"post": {"tags": ["HugeSignInController"], "summary": "选择商品", "operationId": "hugeSignInControllerSelectProduct", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignInSelectProductRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewObject"}}}}}}}, "/rest/wd/summer25/wishTravel/hugeSignIn/resume": {"post": {"tags": ["HugeSignInController"], "summary": "恢复签到", "operationId": "hugeSignInControllerResume", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResumeRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewHugeSignInResumeView"}}}}}}}, "/rest/wd/summer25/wishTravel/hugeSignIn/pay/cancel": {"post": {"tags": ["HugeSignInController"], "summary": "取消支付", "operationId": "hugeSignInControllerCancelPay", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewHugeSignInCancelPayView"}}}}}}}, "/rest/wd/summer25/wishTravel/hugeSignIn/finalLlrewd": {"post": {"tags": ["HugeSignInController"], "summary": "领取最终奖励", "operationId": "hugeSignInControllerFinalLlrewd", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewHugeSignInLlrewdView"}}}}}}}, "/rest/wd/summer25/wishTravel/hugeSignIn/calendar/save": {"post": {"tags": ["HugeSignInController"], "summary": "保存日历", "operationId": "hugeSignInControllerSaveCalendar", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignInCalendarRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewObject"}}}}}}}, "/rest/wd/summer25/wishTravel/hugeSignIn/calendar/delete": {"post": {"tags": ["HugeSignInController"], "summary": "删除日历", "operationId": "hugeSignInControllerDeleteCalendar", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignInCalendarRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewObject"}}}}}}}, "/rest/wd/summer25/wishTravel/hugeSignIn/abandon": {"post": {"tags": ["HugeSignInController"], "summary": "放弃签到", "operationId": "hugeSignInControllerAbandon", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewHugeSignInResumeView"}}}}}}}, "/rest/wd/summer25/wishTravel/chess/move": {"post": {"tags": ["ChessController"], "summary": "向前冲接口", "operationId": "chessControllerChessMove", "parameters": [{"name": "useSignFreeCard", "in": "query", "description": "是否使用免签卡", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewChessMoveResultView"}}}}}}}, "/rest/wd/summer25/wishTravel/team/panel": {"get": {"tags": ["Summer2025WishTravelTeamController"], "summary": "组队面板", "operationId": "summer2025WishTravelTeamControllerGetTeamPanel", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewTeamPanelView"}}}}}}}, "/rest/wd/summer25/wishTravel/team/invitationList": {"get": {"tags": ["Summer2025WishTravelTeamController"], "summary": "推荐好友列表", "operationId": "summer2025WishTravelTeamControllerGetInvitationList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewListTeamUserView"}}}}}}}, "/rest/wd/summer25/wishTravel/task/tasks": {"get": {"tags": ["TaskController"], "summary": "任务列表+任务气泡", "operationId": "taskControllerGetTasks", "parameters": [{"name": "imei", "in": "query", "required": false, "schema": {"type": "string", "default": ""}}, {"name": "idfa", "in": "query", "required": false, "schema": {"type": "string", "default": ""}}, {"name": "oaid", "in": "query", "required": false, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewTaskListView"}}}}}}}, "/rest/wd/summer25/wishTravel/share/init": {"get": {"tags": ["homeShareController"], "summary": "分享发邀", "operationId": "shareControllerShareInit", "parameters": [{"name": "subBiz", "in": "query", "description": "分享subBiz", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewSummerShareInitView"}}}}}}}, "/rest/wd/summer25/wishTravel/polling/inpush": {"get": {"tags": ["PollingController"], "summary": "获取inpush信息", "operationId": "pollingControllerInpush", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewSummerWarmupInPushListView"}}}}}}}, "/rest/wd/summer25/wishTravel/hugeSignIn/getSignInCalendar": {"get": {"tags": ["HugeSignInController"], "summary": "获取打卡日历", "operationId": "hugeSignInControllerGetSignInCalendar", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewHugeSignInCalendarView"}}}}}}}, "/rest/wd/summer25/wishTravel/hugeSignIn/getProductList": {"get": {"tags": ["HugeSignInController"], "summary": "获取商品列表", "operationId": "hugeSignInControllerGetProductList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewSignInSelectProductListView"}}}}}}}, "/rest/wd/summer25/wishTravel/homePage/mainInfo": {"get": {"tags": ["homeController"], "summary": "活动首页信息", "operationId": "homeControllerHomePage", "parameters": [{"name": "entry_src", "in": "query", "description": "来源，用于打点监控", "required": false, "schema": {"type": "string", "default": ""}}, {"name": "scene", "in": "query", "description": "场景，值为LUCK_SHAKE_SUDOKU不从弹窗队里中取弹窗，NEED_START_NEW_ROUND代表开启新的一期", "required": false, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultViewHomeView"}}}}}}}}, "components": {"schemas": {"ResultViewObject": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "object"}, "timestamp": {"type": "integer", "format": "int64"}, "hostname": {"type": "string"}, "error_msg": {"type": "string"}}}, "SummerBackFlowParam": {"type": "object", "properties": {"shareCommonParam": {"type": "string", "description": "回流助力参数"}, "taskToken": {"type": "string", "description": "任务列表携带token，后端用于任务上报"}, "shareId": {"type": "string", "description": "分享id"}, "subBiz": {"type": "string", "description": "分享subBiz"}}, "description": "分享回流接口请求参数"}, "ResultViewSummerBackFlowView": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/SummerBackFlowView"}, "timestamp": {"type": "integer", "format": "int64"}, "hostname": {"type": "string"}, "error_msg": {"type": "string"}}}, "SummerBackFlowView": {"type": "object", "properties": {"toast": {"type": "string", "description": "成功或者失败toast"}, "code": {"type": "integer", "description": "错误码 1是成功 其他非成功", "format": "int32"}}, "description": "分享回流接口返回参数"}, "ResultViewWishTravelLLwdwCardView": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/WishTravelLLwdwCardView"}, "timestamp": {"type": "integer", "format": "int64"}, "hostname": {"type": "string"}, "error_msg": {"type": "string"}}}, "WishTravelLLwdwCardView": {"required": ["llchAmount", "orderNo", "payUrl"], "type": "object", "properties": {"llchAmount": {"type": "integer", "description": "金额，单位分", "format": "int64"}, "orderNo": {"type": "string", "description": "卡id"}, "payUrl": {"type": "string", "description": "H5跳转链接"}}}, "SignInSelectProductRequest": {"type": "object", "properties": {"productId": {"type": "integer", "description": "商品ID", "format": "int32"}, "templateId": {"type": "integer", "description": "模板ID", "format": "int32"}}, "description": "选择商品请求参数"}, "ResumeRequest": {"type": "object", "properties": {"useCash": {"type": "boolean"}, "resumeType": {"type": "integer", "format": "int32"}}}, "CommonTaskCalendarInfo": {"required": ["calendarDescription", "calendarTitle", "dailyTimeStampList", "<PERSON><PERSON><PERSON><PERSON>", "scheduleEndTimeStamp", "scheduleStartTimeStamp"], "type": "object", "properties": {"remindPeriod": {"type": "integer", "description": "1单日 2每日", "format": "int64"}, "scheduleStartTimeStamp": {"type": "integer", "description": "日程起始时间", "format": "int64"}, "scheduleEndTimeStamp": {"type": "integer", "description": "日程结束时间", "format": "int64"}, "calendarTitle": {"type": "string", "description": "日历标题"}, "calendarDescription": {"type": "string", "description": "日历描述"}, "kwaiLink": {"type": "string", "description": "主站拉端链接"}, "nebulaLink": {"type": "string", "description": "极速版拉端链接"}, "dailyTimeStampList": {"type": "array", "description": "单日的提醒时刻列表", "items": {"type": "integer", "description": "单日的提醒时刻列表", "format": "int64"}}}, "description": "任务日历信息"}, "CommonTaskUserInfo": {"required": ["headImg", "userId", "userName"], "type": "object", "properties": {"userId": {"type": "integer", "description": "用户id", "format": "int64"}, "userName": {"type": "string", "description": "用户昵称"}, "headImg": {"type": "string", "description": "用户头像"}}, "description": "任务助力信息"}, "ExternalPayInfo": {"type": "object", "properties": {"orderNo": {"type": "string"}, "merchantId": {"type": "string"}}}, "HugeSignInCustomPopup": {"type": "object", "properties": {"dispatchPopup": {"$ref": "#/components/schemas/MatrixPopupInfo"}, "resumePopup": {"$ref": "#/components/schemas/HugeSignInInterruptResumePopup"}, "drawTask": {"$ref": "#/components/schemas/HugeSignInDrawTaskView"}}}, "HugeSignInDoTaskResumeMethod": {"type": "object", "properties": {"title": {"type": "string"}, "subTitle": {"type": "string"}, "cornerText": {"type": "string"}, "buttonText": {"type": "string"}, "hasBadgeIcon": {"type": "boolean"}, "taskCenterTaskDetails": {"type": "array", "items": {"$ref": "#/components/schemas/TaskCenterTaskDetail"}}, "resumeType": {"type": "integer", "format": "int32"}}}, "HugeSignInDrawTaskInviteTask": {"type": "object", "properties": {"noNeedInvite": {"type": "array", "items": {"$ref": "#/components/schemas/HugeSignInDrawTaskItem"}}, "needInvite": {"type": "array", "items": {"$ref": "#/components/schemas/HugeSignInDrawTaskItem"}}}}, "HugeSignInDrawTaskItem": {"type": "object", "properties": {"value": {"type": "integer", "format": "int32"}, "text": {"type": "string"}, "select": {"type": "boolean"}}}, "HugeSignInDrawTaskType": {"type": "number", "enum": [0, 1], "x-ks-enum-detail": [{"name": "NORMAL", "value": 0}, {"name": "INVITE", "value": 1}]}, "HugeSignInDrawTaskView": {"type": "object", "properties": {"unOpenTitle": {"type": "string"}, "unOpenSubTitle": {"type": "string"}, "openedTitle": {"type": "string"}, "openedSubTitle": {"type": "string"}, "normalTask": {"type": "array", "items": {"$ref": "#/components/schemas/HugeSignInDrawTaskItem"}}, "inviteTask": {"$ref": "#/components/schemas/HugeSignInDrawTaskInviteTask"}, "drawTaskType": {"$ref": "#/components/schemas/HugeSignInDrawTaskType"}}}, "HugeSignInFinanceResumeMethod": {"type": "object", "properties": {"title": {"type": "string"}, "subTitle": {"type": "string"}, "cornerText": {"type": "string"}, "buttonText": {"type": "string"}, "hasBadgeIcon": {"type": "boolean"}, "resumeType": {"type": "integer", "format": "int32"}, "continuePay": {"type": "boolean"}, "cashAmount": {"type": "integer", "format": "int64"}, "coinAmount": {"type": "integer", "format": "int64"}, "internalCashAmountFen": {"type": "integer", "format": "int64"}}}, "HugeSignInInterruptResumePopup": {"required": ["popupType"], "type": "object", "allOf": [{"$ref": "#/components/schemas/SummerPopupView"}, {"type": "object", "properties": {"countDownMills": {"type": "integer", "format": "int64"}, "financeMethod": {"$ref": "#/components/schemas/HugeSignInFinanceResumeMethod"}, "shareAssistMethod": {"$ref": "#/components/schemas/HugeSignInShareAssistResumeMethod"}, "doTaskResumeMethod": {"$ref": "#/components/schemas/HugeSignInDoTaskResumeMethod"}}}]}, "HugeSignInProductView": {"type": "object", "properties": {"productName": {"type": "string"}, "productIcon": {"type": "string"}, "originPrice": {"type": "integer", "format": "int64"}, "labels": {"type": "array", "items": {"type": "string"}}, "templateId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "allSignDays": {"type": "integer", "format": "int32"}, "rewardType": {"type": "integer", "format": "int32"}, "selectNumber": {"type": "string"}, "productChannel": {"type": "integer", "format": "int32"}, "localLabels": {"type": "array", "items": {"type": "string"}}, "limitProductRemainCount": {"type": "integer", "format": "int64"}, "limitProductRemainCountText": {"type": "string"}, "productRemainStatus": {"type": "integer", "format": "int32"}, "minusDays": {"type": "integer", "format": "int32"}, "actualNeedSignInDays": {"type": "integer", "format": "int32"}}}, "HugeSignInResumeView": {"type": "object", "properties": {"success": {"type": "boolean"}, "popupView": {"$ref": "#/components/schemas/HugeSignPopupView"}, "payInfo": {"$ref": "#/components/schemas/ExternalPayInfo"}}}, "HugeSignInShareAssistResumeMethod": {"type": "object", "properties": {"title": {"type": "string"}, "subTitle": {"type": "string"}, "cornerText": {"type": "string"}, "buttonText": {"type": "string"}, "hasBadgeIcon": {"type": "boolean"}, "subBiz": {"type": "string"}, "hugeSignInShareToken": {"type": "string"}, "resumeType": {"type": "integer", "format": "int32"}}}, "HugeSignPopupView": {"type": "object", "properties": {"topTitle": {"type": "string"}, "title": {"type": "string"}, "subTitle": {"type": "string"}, "icon": {"type": "string"}, "buttonText": {"type": "string"}, "linkUrl": {"type": "string"}, "countDownTimes": {"type": "integer", "format": "int64"}, "type": {"type": "integer", "format": "int32"}, "products": {"type": "array", "deprecated": true, "items": {"$ref": "#/components/schemas/HugeSignInProductView"}}, "rewardType": {"type": "integer", "format": "int32"}, "amount": {"type": "integer", "format": "int64"}, "extraParams": {"type": "object", "additionalProperties": {"type": "object"}}, "customPopup": {"$ref": "#/components/schemas/HugeSignInCustomPopup"}}}, "LinkTypeEnum": {"type": "string", "description": "按钮点击类型", "enum": ["UNKNOWN", "JUMP_H5", "PULL_NEW_TASK", "PULL_TASK_LIST_PANEL", "LUCK_SHAKE_SUDOKU", "VERSION_UPGRADE", "RESERVATION", "SOCIAL", "KWAI_LINK", "SIGN", "AGAIN", "DONE", "CLOSE", "STATIC_TEXT", "LIVE_POPUP", "COMMON_TASK", "LLWDW", "SHARE", "CLOUD_TRANSITION", "EVE_RESERVATION_LLAWD", "EVE_LIVE", "PULL_BIG_BAG", "TASK_BUBBLE", "GO_PK", "GO_TASK", "THREE_PULL_NEW_TASK", "FINAL_LLRWD", "NEW_ROUND_SIGN", "CHALLENGE_FAIL", "GO_TEAM", "JS_BRIDGE"], "x-ks-enum-detail": [{"name": "UNKNOWN", "value": "UNKNOWN", "description": "未知"}, {"name": "JUMP_H5", "value": "JUMP_H5", "description": "跳转h5"}, {"name": "PULL_NEW_TASK", "value": "PULL_NEW_TASK", "description": "拉新任务"}, {"name": "PULL_TASK_LIST_PANEL", "value": "PULL_TASK_LIST_PANEL", "description": "拉起任务面板"}, {"name": "LUCK_SHAKE_SUDOKU", "value": "LUCK_SHAKE_SUDOKU", "description": "摇一摇抽奖"}, {"name": "VERSION_UPGRADE", "value": "VERSION_UPGRADE", "description": "版本升级"}, {"name": "RESERVATION", "value": "RESERVATION", "description": "预约"}, {"name": "SOCIAL", "value": "SOCIAL", "description": "社交私信"}, {"name": "KWAI_LINK", "value": "KWAI_LINK", "description": "快链跳转"}, {"name": "SIGN", "value": "SIGN", "description": "签到"}, {"name": "AGAIN", "value": "AGAIN", "description": "签到按钮:明天再来"}, {"name": "DONE", "value": "DONE", "description": "签到按钮:z已领完"}, {"name": "CLOSE", "value": "CLOSE", "description": "关闭"}, {"name": "STATIC_TEXT", "value": "STATIC_TEXT", "description": "静态文案"}, {"name": "LIVE_POPUP", "value": "LIVE_POPUP", "description": "直播弹窗"}, {"name": "COMMON_TASK", "value": "COMMON_TASK", "description": "任务"}, {"name": "LLWDW", "value": "LLWDW", "description": "提现"}, {"name": "SHARE", "value": "SHARE", "description": "分享"}, {"name": "CLOUD_TRANSITION", "value": "CLOUD_TRANSITION", "description": "云转场"}, {"name": "EVE_RESERVATION_LLAWD", "value": "EVE_RESERVATION_LLAWD", "description": "除夕-预约分大奖"}, {"name": "EVE_LIVE", "value": "EVE_LIVE", "description": "除夕-直播引导"}, {"name": "PULL_BIG_BAG", "value": "PULL_BIG_BAG", "description": "拉起大礼包抽奖"}, {"name": "TASK_BUBBLE", "value": "TASK_BUBBLE", "description": "任务气泡"}, {"name": "GO_PK", "value": "GO_PK", "description": "PK会场"}, {"name": "GO_TASK", "value": "GO_TASK", "description": "去完成任务"}, {"name": "THREE_PULL_NEW_TASK", "value": "THREE_PULL_NEW_TASK", "description": "三合一拉新任务"}, {"name": "FINAL_LLRWD", "value": "FINAL_LLRWD", "description": "最终大奖领奖"}, {"name": "NEW_ROUND_SIGN", "value": "NEW_ROUND_SIGN", "description": "开启新一轮长签活动"}, {"name": "CHALLENGE_FAIL", "value": "CHALLENGE_FAIL", "description": "断签-挑战失败"}, {"name": "GO_TEAM", "value": "GO_TEAM", "description": "去组队"}, {"name": "JS_BRIDGE", "value": "JS_BRIDGE", "description": "调用客户端桥"}]}, "MatrixButtonInfo": {"type": "object", "properties": {"text": {"type": "string"}, "linkUrl": {"type": "string"}, "linkType": {"type": "integer", "format": "int32"}, "cornerText": {"type": "string"}, "cornerIconUrl": {"type": "string"}, "extParams": {"type": "object", "additionalProperties": {"type": "object"}}, "eventTrackingLogInfo": {"$ref": "#/components/schemas/ResourceSlotInfo"}}}, "MatrixPopupInfo": {"type": "object", "properties": {"title": {"type": "string"}, "subTitle": {"type": "string"}, "bgImgUrl": {"type": "string"}, "buttonInfo": {"$ref": "#/components/schemas/MatrixButtonInfo"}, "lastTaskCompleteBtnToast": {"type": "string"}, "extParams": {"type": "object", "additionalProperties": {"type": "object"}}, "eventTrackingLogInfo": {"$ref": "#/components/schemas/ResourceSlotInfo"}}}, "PopupType": {"type": "string", "description": "弹窗类型", "enum": ["UNKNOWN", "GOLDEN_TREE", "COLLECT_CARD_PARTITION", "BEGINNER_GUIDE", "LLRP_PROGRESS_LLCH", "LLRP_PROGRESS_GOLD_LLCN", "LLRP_PROGRESS_PAYMENT", "LLRP_PROGRESS_WITH_DRAW_CARD", "CRITICAL_MOMENT", "SHARE_VIDEO_TASK", "PK_TASK_SUCCESS", "PK_TASK_FLAT", "TIME_ASSIST_TASK_SUCCESS", "TIME_ASSIST_RETURN_TASK_SUCCESS", "TIME_LIMIT_TASK_SUCCESS", "ASSIST_DOUBLE_LLCH_TASK_SUCCESS", "ASSIST_DOUBLE_LLCN_TASK_SUCCESS", "ALWAYS_PULL_NEW_TASK", "COMMON_LLCH_TASK", "PK_TASK_FAIL", "TIME_ASSIST_TASK_FAIL", "TIME_LIMIT_TASK_FAIL", "ASSIST_DOUBLE_LLCH_TASK_FAIL", "ASSIST_DOUBLE_LLCN_TASK_FAIL", "LINK_MARKETING_MATCH", "PK_MARKETING_MATCH", "TASK_MARKETING_MATCH", "SIGN_MARKETING_MATCH", "RESERVATION_KOC_LLAWD", "RESERVATION_NORMAL_LLAWD", "LS_LLCH_LLREWD", "LS_LLCN_LLREWD", "LS_COUPON_LLREWD", "LS_WITH_DRAW_CARD_LLREWD", "LS_LLCH_TRANSFER_CARD_LLREWD", "LS_PROFILE_PENDANT_LLREWD", "LS_BRANCH_VENUE_CARD_LLREWD", "LS_KOI_PROPS_LLREWD", "LS_SOCIAL_PROPS_LLREWD", "LS_COMMON_ZT_TASK_LLCN", "LS_COMMON_ZT_TASK_LLCH", "LS_FOLLOW_TASK_LLCN", "LS_FOLLOW_TASK_LLCH", "LS_FOLLOW_TASK_BLESS", "LS_WATCH_LIVE_TASK_LLCN", "LS_WATCH_LIVE_TASK_LLCH", "LS_RESERVE_WATCH_LIVE_LLCH", "LS_RESERVE_WATCH_LIVE_LLCN", "LS_RESERVE_LEEE_RAIN_LLCH", "LS_RESERVE_LEEE_RAIN_LLCN", "LS_INVOKE_APP_LLCH", "LS_INVOKE_APP_LLCN", "LS_HUG_SIGN_LLREWD", "LS_HUG_SIGN_SELECTED_GIFT", "LS_RECRUIT_DIVERSION_DIRECT_PASS_LLREWD", "LS_RECRUIT_STATIC_POPUP_LLREWD", "LS_FISSION_BUBBLE_DIVERSION_LLREWD", "LS_SOCIAL_POPUP_DIVERSION_LLREWD", "LS_SOCIAL_LLMI_LLGE", "LS_TIME_LIMITED_COUNT_TASK_LLCN", "LS_TIME_LIMITED_COUNT_TASK_LLCH", "LS_TIME_LIMITED_ASSIST_TASK_LLCN", "LS_TIME_LIMITED_ASSIST_TASK_LLCH", "LS_TIME_LIMITED_REFLUX_ASSIST_TASK_LLCH", "LS_TIME_LIMITED_REFLUX_ASSIST_TASK_LLCN", "LS_TIME_TIME_LIMITED_PK_TASK", "LS_TIME_LIMITED_ASSIST_DOUBLE_LLCN", "LS_TIME_LIMITED_ASSIST_DOUBLE_LLCH", "LS_AD_VIDEO", "LS_DEFAULT_LLCN", "LS_DEFAULT_BLESS", "LS_AD_PHOTO", "LS_STATIC_POPUP_DIVERSION", "LS_RECRUIT_CAMPUS_TRIAL_CARD", "LS_RECRUIT_CAMPUS_TIME_LIMIT_CARD", "LS_RECRUIT_BOOM_ORDER_TRIAL_CARD", "LS_RECRUIT_BOOM_ORDER_TIME_LIMIT_CARD", "LS_SHARE_LLCN_TASK", "LS_PUSH_SWITCH_LLCN_TASK", "LS_WATCH_VIDEO_LLCN_TASK", "LS_RECRUIT_QUICK_CARD_TRIAL", "LS_RECRUIT_QUICK_CARD_DISCOUNT", "LS_RECRUIT_AI_BOOST_PACK_TRAIL", "LS_IDEAT_BOOM_ORDER_CARD_TRIAL", "LS_IDEAT_BOOM_ORDER_CARD_TIME_LIMIT_DISCOUNT", "LS_DREAM_BUILDER_BOOM_ORDER_CARD_TRIAL", "LS_DREAM_BUILDER_BOOM_ORDER_CARD_TIME_LIMIT_DISCOUNT", "PARTITION_LLCH_PREVIEW", "EVE_LS_GOLD_BAR_LLREWD", "EVE_LS_FISSION_GOAT_DIVERSION_LLREWD", "EVE_LS_RESERVE_LEEE_RAIN_SHAKE", "EVE_LS_INVOKE_APP_SHAKE", "EVE_LS_WATCH_LIVE_TASK_SHAKE", "EVE_LS_FOLLOW_TASK_SHAKE", "EVE_BIG_BAG_DRAW", "EVE_PARTITION_LLCH_DRAW", "EVE_RESERBATION_OVERDUE", "PK_RECEIVE_ASSIST_PK", "SETTLE_PK", "PK_TASK_COMPLETE", "TL_ALWAYS_PULL_TASK", "HUGE_SIGN_IN_LLAWD", "HUGE_SIGN_IN_PRODUCT", "HUGE_SIGN_IN_RESUME", "HUGE_SIGN_IN_CHALLENGE_FAILURE", "HUGE_SIGN_IN_COIN_NOT_ENOUGH", "TEAM_TASK", "TEAM_EXIT", "TEAM_SUCCESS", "TEAM_SIGN_REWARD", "FINAL_RECEIVE_LLAWD", "FINAL_RECEIVE_EXPIRE", "HUGE_SIGN_IN_STATION_POPUP", "HUGE_SIGN_IN_STATION_NEW_POPUP", "HUGE_SIGN_IN_STATION_NEW_COUPON_POPUP", "OLD_USER_TIP"], "x-ks-enum-detail": [{"name": "UNKNOWN", "value": "UNKNOWN", "description": "未知"}, {"name": "GOLDEN_TREE", "value": "GOLDEN_TREE", "description": "升级黄金树"}, {"name": "COLLECT_CARD_PARTITION", "value": "COLLECT_CARD_PARTITION", "description": "集卡小年瓜分"}, {"name": "BEGINNER_GUIDE", "value": "BEGINNER_GUIDE", "description": "新手引导"}, {"name": "LLRP_PROGRESS_LLCH", "value": "LLRP_PROGRESS_LLCH", "description": "进度红包开奖-现金"}, {"name": "LLRP_PROGRESS_GOLD_LLCN", "value": "LLRP_PROGRESS_GOLD_LLCN", "description": "进度红包开奖-金币"}, {"name": "LLRP_PROGRESS_PAYMENT", "value": "LLRP_PROGRESS_PAYMENT", "description": "进度红包开奖-打款"}, {"name": "LLRP_PROGRESS_WITH_DRAW_CARD", "value": "LLRP_PROGRESS_WITH_DRAW_CARD", "description": "进度红包开奖-提现卡"}, {"name": "CRITICAL_MOMENT", "value": "CRITICAL_MOMENT", "description": "暴击时刻"}, {"name": "SHARE_VIDEO_TASK", "value": "SHARE_VIDEO_TASK", "description": "魔表任务"}, {"name": "PK_TASK_SUCCESS", "value": "PK_TASK_SUCCESS", "description": "限时pk赢or平局"}, {"name": "PK_TASK_FLAT", "value": "PK_TASK_FLAT", "description": "限时pk平局"}, {"name": "TIME_ASSIST_TASK_SUCCESS", "value": "TIME_ASSIST_TASK_SUCCESS", "description": "限时拉人成功"}, {"name": "TIME_ASSIST_RETURN_TASK_SUCCESS", "value": "TIME_ASSIST_RETURN_TASK_SUCCESS", "description": "限时拉回成功"}, {"name": "TIME_LIMIT_TASK_SUCCESS", "value": "TIME_LIMIT_TASK_SUCCESS", "description": "限时挑战任务完成"}, {"name": "ASSIST_DOUBLE_LLCH_TASK_SUCCESS", "value": "ASSIST_DOUBLE_LLCH_TASK_SUCCESS", "description": "拉人翻倍成功"}, {"name": "ASSIST_DOUBLE_LLCN_TASK_SUCCESS", "value": "ASSIST_DOUBLE_LLCN_TASK_SUCCESS", "description": "拉人翻倍金币成功"}, {"name": "ALWAYS_PULL_NEW_TASK", "value": "ALWAYS_PULL_NEW_TASK", "description": "常驻拉人"}, {"name": "COMMON_LLCH_TASK", "value": "COMMON_LLCH_TASK", "description": "通用发现金任务"}, {"name": "PK_TASK_FAIL", "value": "PK_TASK_FAIL", "description": "限时pk失败"}, {"name": "TIME_ASSIST_TASK_FAIL", "value": "TIME_ASSIST_TASK_FAIL", "description": "限时拉人未完成"}, {"name": "TIME_LIMIT_TASK_FAIL", "value": "TIME_LIMIT_TASK_FAIL", "description": "限时挑战任务未完成"}, {"name": "ASSIST_DOUBLE_LLCH_TASK_FAIL", "value": "ASSIST_DOUBLE_LLCH_TASK_FAIL", "description": "拉人翻倍失败"}, {"name": "ASSIST_DOUBLE_LLCN_TASK_FAIL", "value": "ASSIST_DOUBLE_LLCN_TASK_FAIL", "description": "拉人翻倍得金币失败"}, {"name": "LINK_MARKETING_MATCH", "value": "LINK_MARKETING_MATCH", "description": "链接类营销弹窗"}, {"name": "PK_MARKETING_MATCH", "value": "PK_MARKETING_MATCH", "description": "PK类营销弹窗"}, {"name": "TASK_MARKETING_MATCH", "value": "TASK_MARKETING_MATCH", "description": "任务类营销弹窗"}, {"name": "SIGN_MARKETING_MATCH", "value": "SIGN_MARKETING_MATCH", "description": "长签类营销弹窗"}, {"name": "RESERVATION_KOC_LLAWD", "value": "RESERVATION_KOC_LLAWD", "description": "预约瓜分-KOC人群"}, {"name": "RESERVATION_NORMAL_LLAWD", "value": "RESERVATION_NORMAL_LLAWD", "description": "预约瓜分-普通人群"}, {"name": "LS_LLCH_LLREWD", "value": "LS_LLCH_LLREWD", "description": "向前冲现金奖励"}, {"name": "LS_LLCN_LLREWD", "value": "LS_LLCN_LLREWD", "description": "向前冲金币奖励"}, {"name": "LS_COUPON_LLREWD", "value": "LS_COUPON_LLREWD", "description": "向前冲优惠券奖励"}, {"name": "LS_WITH_DRAW_CARD_LLREWD", "value": "LS_WITH_DRAW_CARD_LLREWD", "description": "向前冲提现卡奖励"}, {"name": "LS_LLCH_TRANSFER_CARD_LLREWD", "value": "LS_LLCH_TRANSFER_CARD_LLREWD", "description": "向前冲现金打款奖励"}, {"name": "LS_PROFILE_PENDANT_LLREWD", "value": "LS_PROFILE_PENDANT_LLREWD", "description": "向前冲页挂件奖励"}, {"name": "LS_BRANCH_VENUE_CARD_LLREWD", "value": "LS_BRANCH_VENUE_CARD_LLREWD", "description": "向前冲福卡奖励"}, {"name": "LS_KOI_PROPS_LLREWD", "value": "LS_KOI_PROPS_LLREWD", "description": "向前冲锦鲤道具奖励"}, {"name": "LS_SOCIAL_PROPS_LLREWD", "value": "LS_SOCIAL_PROPS_LLREWD", "description": "向前冲社交道具奖励"}, {"name": "LS_COMMON_ZT_TASK_LLCN", "value": "LS_COMMON_ZT_TASK_LLCN", "description": "向前冲通用中台任务-发金币奖励"}, {"name": "LS_COMMON_ZT_TASK_LLCH", "value": "LS_COMMON_ZT_TASK_LLCH", "description": "向前冲通用中台任务-发现金奖励"}, {"name": "LS_FOLLOW_TASK_LLCN", "value": "LS_FOLLOW_TASK_LLCN", "description": "向前冲下发发金币的关注任务"}, {"name": "LS_FOLLOW_TASK_LLCH", "value": "LS_FOLLOW_TASK_LLCH", "description": "向前冲下发发现金的关注任务"}, {"name": "LS_FOLLOW_TASK_BLESS", "value": "LS_FOLLOW_TASK_BLESS", "description": "向前冲下发发祝福语的关注任务"}, {"name": "LS_WATCH_LIVE_TASK_LLCN", "value": "LS_WATCH_LIVE_TASK_LLCN", "description": "向前冲下发发金币的观看直播的任务"}, {"name": "LS_WATCH_LIVE_TASK_LLCH", "value": "LS_WATCH_LIVE_TASK_LLCH", "description": "向前冲下发发现金的观看直播的任务"}, {"name": "LS_RESERVE_WATCH_LIVE_LLCH", "value": "LS_RESERVE_WATCH_LIVE_LLCH", "description": "向前冲下发发现金的预约直播的任务"}, {"name": "LS_RESERVE_WATCH_LIVE_LLCN", "value": "LS_RESERVE_WATCH_LIVE_LLCN", "description": "向前冲下发发金币的预约直播的任务"}, {"name": "LS_RESERVE_LEEE_RAIN_LLCH", "value": "LS_RESERVE_LEEE_RAIN_LLCH", "description": "向前冲下发发现金的预约红包雨任务"}, {"name": "LS_RESERVE_LEEE_RAIN_LLCN", "value": "LS_RESERVE_LEEE_RAIN_LLCN", "description": "向前冲下发发金币的预约红包雨任务"}, {"name": "LS_INVOKE_APP_LLCH", "value": "LS_INVOKE_APP_LLCH", "description": "向前冲下发发现金的双端互拉任务"}, {"name": "LS_INVOKE_APP_LLCN", "value": "LS_INVOKE_APP_LLCN", "description": "向前冲下发发金币的双端互拉任务"}, {"name": "LS_HUG_SIGN_LLREWD", "value": "LS_HUG_SIGN_LLREWD", "description": "向前冲下发长签导流"}, {"name": "LS_HUG_SIGN_SELECTED_GIFT", "value": "LS_HUG_SIGN_SELECTED_GIFT", "description": "向前冲下发长签导流"}, {"name": "LS_RECRUIT_DIVERSION_DIRECT_PASS_LLREWD", "value": "LS_RECRUIT_DIVERSION_DIRECT_PASS_LLREWD", "description": "向前冲下发招聘职位直通卡"}, {"name": "LS_RECRUIT_STATIC_POPUP_LLREWD", "value": "LS_RECRUIT_STATIC_POPUP_LLREWD", "description": "向前冲下发招聘静态导流卡"}, {"name": "LS_FISSION_BUBBLE_DIVERSION_LLREWD", "value": "LS_FISSION_BUBBLE_DIVERSION_LLREWD", "description": "向前冲下发泡泡红包导流"}, {"name": "LS_SOCIAL_POPUP_DIVERSION_LLREWD", "value": "LS_SOCIAL_POPUP_DIVERSION_LLREWD", "description": "向前冲下发社交会场导流"}, {"name": "LS_SOCIAL_LLMI_LLGE", "value": "LS_SOCIAL_LLMI_LLGE", "description": "向前冲下发社交会场火崽崽小游戏"}, {"name": "LS_TIME_LIMITED_COUNT_TASK_LLCN", "value": "LS_TIME_LIMITED_COUNT_TASK_LLCN", "description": "向前冲下发金币的限时任务，完成一定数量任务"}, {"name": "LS_TIME_LIMITED_COUNT_TASK_LLCH", "value": "LS_TIME_LIMITED_COUNT_TASK_LLCH", "description": "向前冲下发金币的限时任务，完成一定数量任务"}, {"name": "LS_TIME_LIMITED_ASSIST_TASK_LLCN", "value": "LS_TIME_LIMITED_ASSIST_TASK_LLCN", "description": "向前冲下发限时拉人任务-不限人群，发金币"}, {"name": "LS_TIME_LIMITED_ASSIST_TASK_LLCH", "value": "LS_TIME_LIMITED_ASSIST_TASK_LLCH", "description": "向前冲下发限时拉人任务-不限人群，发现金"}, {"name": "LS_TIME_LIMITED_REFLUX_ASSIST_TASK_LLCH", "value": "LS_TIME_LIMITED_REFLUX_ASSIST_TASK_LLCH", "description": "向前冲下发限时拉人任务-拉活，发现金"}, {"name": "LS_TIME_LIMITED_REFLUX_ASSIST_TASK_LLCN", "value": "LS_TIME_LIMITED_REFLUX_ASSIST_TASK_LLCN", "description": "向前冲下发限时拉人任务-拉活，发金币"}, {"name": "LS_TIME_TIME_LIMITED_PK_TASK", "value": "LS_TIME_TIME_LIMITED_PK_TASK", "description": "向前冲下发限时PK任务"}, {"name": "LS_TIME_LIMITED_ASSIST_DOUBLE_LLCN", "value": "LS_TIME_LIMITED_ASSIST_DOUBLE_LLCN", "description": "向前冲下发限时拉人翻倍，发金币"}, {"name": "LS_TIME_LIMITED_ASSIST_DOUBLE_LLCH", "value": "LS_TIME_LIMITED_ASSIST_DOUBLE_LLCH", "description": "向前冲下发限时PK任务，发现金"}, {"name": "LS_AD_VIDEO", "value": "LS_AD_VIDEO", "description": "向前冲商业化广告"}, {"name": "LS_DEFAULT_LLCN", "value": "LS_DEFAULT_LLCN", "description": "向前冲兜底金币奖励"}, {"name": "LS_DEFAULT_BLESS", "value": "LS_DEFAULT_BLESS", "description": "向前冲兜底祝福语"}, {"name": "LS_AD_PHOTO", "value": "LS_AD_PHOTO", "description": "向前冲商业化广告"}, {"name": "LS_STATIC_POPUP_DIVERSION", "value": "LS_STATIC_POPUP_DIVERSION", "description": "向前冲商业化导流"}, {"name": "LS_RECRUIT_CAMPUS_TRIAL_CARD", "value": "LS_RECRUIT_CAMPUS_TRIAL_CARD", "description": "向前冲下发招聘职校招试用卡"}, {"name": "LS_RECRUIT_CAMPUS_TIME_LIMIT_CARD", "value": "LS_RECRUIT_CAMPUS_TIME_LIMIT_CARD", "description": "向前冲下发招聘校招限时卡"}, {"name": "LS_RECRUIT_BOOM_ORDER_TRIAL_CARD", "value": "LS_RECRUIT_BOOM_ORDER_TRIAL_CARD", "description": "向前冲下发招聘爆单试用卡"}, {"name": "LS_RECRUIT_BOOM_ORDER_TIME_LIMIT_CARD", "value": "LS_RECRUIT_BOOM_ORDER_TIME_LIMIT_CARD", "description": "向前冲下发招聘爆单限时卡"}, {"name": "LS_SHARE_LLCN_TASK", "value": "LS_SHARE_LLCN_TASK", "description": "向前冲下发分享得金币任务"}, {"name": "LS_PUSH_SWITCH_LLCN_TASK", "value": "LS_PUSH_SWITCH_LLCN_TASK", "description": "向前冲下发打开push开关得金币任务"}, {"name": "LS_WATCH_VIDEO_LLCN_TASK", "value": "LS_WATCH_VIDEO_LLCN_TASK", "description": "向前冲下发观看视频得金币任务"}, {"name": "LS_RECRUIT_QUICK_CARD_TRIAL", "value": "LS_RECRUIT_QUICK_CARD_TRIAL", "description": "向前冲下发快聘快招卡"}, {"name": "LS_RECRUIT_QUICK_CARD_DISCOUNT", "value": "LS_RECRUIT_QUICK_CARD_DISCOUNT", "description": "向前冲下发快聘快招卡优惠"}, {"name": "LS_RECRUIT_AI_BOOST_PACK_TRAIL", "value": "LS_RECRUIT_AI_BOOST_PACK_TRAIL", "description": "向前冲下发快聘AI加量包"}, {"name": "LS_IDEAT_BOOM_ORDER_CARD_TRIAL", "value": "LS_IDEAT_BOOM_ORDER_CARD_TRIAL", "description": "向前冲下发理想家爆单卡"}, {"name": "LS_IDEAT_BOOM_ORDER_CARD_TIME_LIMIT_DISCOUNT", "value": "LS_IDEAT_BOOM_ORDER_CARD_TIME_LIMIT_DISCOUNT", "description": "向前冲下发理想家爆单卡优惠"}, {"name": "LS_DREAM_BUILDER_BOOM_ORDER_CARD_TRIAL", "value": "LS_DREAM_BUILDER_BOOM_ORDER_CARD_TRIAL", "description": "向前冲下发筑梦家爆单卡"}, {"name": "LS_DREAM_BUILDER_BOOM_ORDER_CARD_TIME_LIMIT_DISCOUNT", "value": "LS_DREAM_BUILDER_BOOM_ORDER_CARD_TIME_LIMIT_DISCOUNT", "description": "向前冲下发筑梦家爆单卡限时优惠"}, {"name": "PARTITION_LLCH_PREVIEW", "value": "PARTITION_LLCH_PREVIEW", "description": "分万元现金预告弹窗"}, {"name": "EVE_LS_GOLD_BAR_LLREWD", "value": "EVE_LS_GOLD_BAR_LLREWD", "description": "向前冲金条奖励"}, {"name": "EVE_LS_FISSION_GOAT_DIVERSION_LLREWD", "value": "EVE_LS_FISSION_GOAT_DIVERSION_LLREWD", "description": "向前冲下发羊头导流"}, {"name": "EVE_LS_RESERVE_LEEE_RAIN_SHAKE", "value": "EVE_LS_RESERVE_LEEE_RAIN_SHAKE", "description": "向前冲下发发向前冲次数的预约红包雨任务"}, {"name": "EVE_LS_INVOKE_APP_SHAKE", "value": "EVE_LS_INVOKE_APP_SHAKE", "description": "向前冲下发发向前冲次数的双端互拉任务"}, {"name": "EVE_LS_WATCH_LIVE_TASK_SHAKE", "value": "EVE_LS_WATCH_LIVE_TASK_SHAKE", "description": "向前冲下发发向前冲次数的观看直播任务"}, {"name": "EVE_LS_FOLLOW_TASK_SHAKE", "value": "EVE_LS_FOLLOW_TASK_SHAKE", "description": "向前冲下发发向前冲次数的关注任务"}, {"name": "EVE_BIG_BAG_DRAW", "value": "EVE_BIG_BAG_DRAW", "description": "大礼包抽奖弹窗"}, {"name": "EVE_PARTITION_LLCH_DRAW", "value": "EVE_PARTITION_LLCH_DRAW"}, {"name": "EVE_RESERBATION_OVERDUE", "value": "EVE_RESERBATION_OVERDUE", "description": "留存预约瓜分错过弹窗"}, {"name": "PK_RECEIVE_ASSIST_PK", "value": "PK_RECEIVE_ASSIST_PK", "description": "真人pk有人给你助力提醒弹窗"}, {"name": "SETTLE_PK", "value": "SETTLE_PK", "description": "真人pk结算弹窗"}, {"name": "PK_TASK_COMPLETE", "value": "PK_TASK_COMPLETE", "description": "pk任务完成弹窗"}, {"name": "TL_ALWAYS_PULL_TASK", "value": "TL_ALWAYS_PULL_TASK", "description": "常驻拉人-不限人群-拉起弹窗"}, {"name": "HUGE_SIGN_IN_LLAWD", "value": "HUGE_SIGN_IN_LLAWD", "description": "签到成功奖品弹窗"}, {"name": "HUGE_SIGN_IN_PRODUCT", "value": "HUGE_SIGN_IN_PRODUCT", "description": "签到成功选品弹窗"}, {"name": "HUGE_SIGN_IN_RESUME", "value": "HUGE_SIGN_IN_RESUME", "description": "正常续签弹窗"}, {"name": "HUGE_SIGN_IN_CHALLENGE_FAILURE", "value": "HUGE_SIGN_IN_CHALLENGE_FAILURE", "description": "挑战失败弹窗"}, {"name": "HUGE_SIGN_IN_COIN_NOT_ENOUGH", "value": "HUGE_SIGN_IN_COIN_NOT_ENOUGH", "description": "金币不足弹窗"}, {"name": "TEAM_TASK", "value": "TEAM_TASK", "description": "组队任务"}, {"name": "TEAM_EXIT", "value": "TEAM_EXIT", "description": "退出队伍弹窗"}, {"name": "TEAM_SUCCESS", "value": "TEAM_SUCCESS", "description": "组队成功弹窗"}, {"name": "TEAM_SIGN_REWARD", "value": "TEAM_SIGN_REWARD", "description": "队伍打卡完成发奖弹窗"}, {"name": "FINAL_RECEIVE_LLAWD", "value": "FINAL_RECEIVE_LLAWD", "description": "最终大奖领奖弹窗"}, {"name": "FINAL_RECEIVE_EXPIRE", "value": "FINAL_RECEIVE_EXPIRE", "description": "最终大奖过期弹窗"}, {"name": "HUGE_SIGN_IN_STATION_POPUP", "value": "HUGE_SIGN_IN_STATION_POPUP", "description": "站点奖励弹窗"}, {"name": "HUGE_SIGN_IN_STATION_NEW_POPUP", "value": "HUGE_SIGN_IN_STATION_NEW_POPUP", "description": "新回用户预告奖励弹窗"}, {"name": "HUGE_SIGN_IN_STATION_NEW_COUPON_POPUP", "value": "HUGE_SIGN_IN_STATION_NEW_COUPON_POPUP", "description": "新回用户预告奖励券弹窗"}, {"name": "OLD_USER_TIP", "value": "OLD_USER_TIP", "description": "存量用户提示弹窗"}]}, "ResourceSlotInfo": {"type": "object", "properties": {"deliverOrderId": {"type": "string"}, "materialKey": {"type": "string"}, "eventTrackingTaskId": {"type": "integer", "format": "int64"}, "resourceId": {"type": "string"}, "extParams": {"type": "object", "additionalProperties": {"type": "object"}}}}, "ResultViewHugeSignInResumeView": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/HugeSignInResumeView"}, "timestamp": {"type": "integer", "format": "int64"}, "hostname": {"type": "string"}, "error_msg": {"type": "string"}}}, "SummerCommonButtonView": {"type": "object", "properties": {"linkType": {"$ref": "#/components/schemas/LinkTypeEnum"}, "linkText": {"type": "string", "description": "按钮文案"}, "linkSubText": {"type": "string", "description": "按钮子文案"}, "linkUrl": {"type": "string", "description": "跳转链接"}, "icon": {"type": "string", "description": "icon"}}, "description": "通用按钮", "discriminator": {"propertyName": "linkType", "mapping": {"SOCIAL": "#/components/schemas/SummerWarmupSocialButtonView", "JUMP_H5": "#/components/schemas/SummerDefaultButtonView", "PULL_NEW_TASK": "#/components/schemas/SummerDefaultButtonView", "PULL_TASK_LIST_PANEL": "#/components/schemas/SummerDefaultButtonView", "LUCK_SHAKE_SUDOKU": "#/components/schemas/SummerDefaultButtonView", "VERSION_UPGRADE": "#/components/schemas/SummerDefaultButtonView", "RESERVATION": "#/components/schemas/SummerDefaultButtonView", "KWAI_LINK": "#/components/schemas/SummerDefaultButtonView", "SIGN": "#/components/schemas/SummerDefaultButtonView", "AGAIN": "#/components/schemas/SummerDefaultButtonView", "DONE": "#/components/schemas/SummerDefaultButtonView", "CLOSE": "#/components/schemas/SummerDefaultButtonView", "STATIC_TEXT": "#/components/schemas/SummerDefaultButtonView", "LLWDW": "#/components/schemas/SummerDefaultButtonView", "COMMON_TASK": "#/components/schemas/SummerDefaultButtonView", "CLOUD_TRANSITION": "#/components/schemas/SummerDefaultButtonView", "SHARE": "#/components/schemas/SummerDefaultButtonView", "PULL_BIG_BAG": "#/components/schemas/SummerDefaultButtonView", "GO_PK": "#/components/schemas/SummerDefaultButtonView", "GO_TASK": "#/components/schemas/SummerDefaultButtonView", "FINAL_LLRWD": "#/components/schemas/SummerDefaultButtonView", "NEW_ROUND_SIGN": "#/components/schemas/SummerDefaultButtonView", "CHALLENGE_FAIL": "#/components/schemas/SummerDefaultButtonView", "GO_TEAM": "#/components/schemas/SummerDefaultButtonView", "JS_BRIDGE": "#/components/schemas/SummerDefaultButtonView"}}, "oneOf": [{"$ref": "#/components/schemas/SummerWarmupSocialButtonView"}, {"$ref": "#/components/schemas/SummerDefaultButtonView"}]}, "SummerDefaultButtonView": {"type": "object", "description": "通用默认按钮", "allOf": [{"$ref": "#/components/schemas/SummerCommonButtonView"}]}, "SummerWarmupSocialButtonView": {"type": "object", "description": "社交按钮", "allOf": [{"$ref": "#/components/schemas/SummerCommonButtonView"}, {"type": "object", "properties": {"emotionId": {"type": "string", "description": "大表情id"}, "sense": {"type": "integer", "description": "发信场景", "format": "int32"}, "extraInfo": {"type": "string", "description": "snackBar的相关信息"}, "packageId": {"type": "string", "description": "表情包id"}, "linkIcon": {"type": "string", "description": "按钮的icon"}, "buttonActionedText": {"type": "string", "description": "发完私信后的按钮文案"}}}]}, "TaskCenterAssistUserInfo": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "username": {"type": "string"}, "headImg": {"type": "string"}}}, "TaskCenterButton": {"type": "object", "properties": {"displayText": {"type": "string"}, "jumpLink": {"type": "string"}, "jumpType": {"type": "string"}}}, "TaskCenterPrizeDetail": {"type": "object", "properties": {"prizeId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "prizeType": {"$ref": "#/components/schemas/WishTravelPrizeType"}, "amount": {"type": "integer", "format": "int64"}}}, "TaskCenterTaskDetail": {"type": "object", "properties": {"taskId": {"type": "integer", "format": "int64"}, "title": {"type": "string"}, "description": {"type": "string"}, "iconUrls": {"type": "array", "items": {"type": "string"}}, "completeConditionAmount": {"type": "integer", "format": "int64"}, "completedAmount": {"type": "integer", "format": "int64"}, "completeMaxTimes": {"type": "integer", "format": "int64"}, "completedTimes": {"type": "integer", "format": "int64"}, "taskStatus": {"$ref": "#/components/schemas/TaskCenterTaskStatus"}, "button": {"$ref": "#/components/schemas/TaskCenterButton"}, "taskCenterPrizeDetail": {"$ref": "#/components/schemas/TaskCenterPrizeDetail"}, "extParam": {"type": "string"}, "completeToastText": {"type": "string"}, "timeLimitStartTime": {"type": "integer", "format": "int64"}, "timeLimitEndTime": {"type": "integer", "format": "int64"}, "timeLimitExpireTime": {"type": "integer", "format": "int64"}, "timeLimitedType": {"type": "integer", "format": "int32"}, "takeTime": {"type": "integer", "format": "int64"}, "delayQueryStatus": {"type": "boolean"}, "widgetParam": {"type": "string"}, "taskShowStyle": {"type": "string"}, "apkName": {"type": "string"}, "apkAddr": {"type": "string"}, "iosStoreAddr": {"type": "string"}, "iosSchema": {"type": "string"}, "shareSubBiz": {"type": "string"}, "unsupportedToast": {"type": "string"}, "taskPriority": {"type": "integer", "format": "int64"}, "taskContentId": {"type": "string"}, "taskToken": {"type": "string"}, "subBizId": {"type": "integer", "format": "int64"}, "takeType": {"type": "string"}, "takeStatus": {"type": "string"}, "reservationCalendarConfig": {"$ref": "#/components/schemas/CommonTaskCalendarInfo"}, "assistUserInfos": {"type": "array", "items": {"$ref": "#/components/schemas/TaskCenterAssistUserInfo"}}, "relationChainInfo": {"type": "array", "items": {"$ref": "#/components/schemas/CommonTaskUserInfo"}}, "relationChainTypes": {"type": "array", "items": {"type": "string"}}, "prizeIconUrls": {"type": "array", "items": {"type": "string"}}, "completeTime": {"type": "integer", "format": "int64"}}}, "TaskCenterTaskStatus": {"type": "string", "enum": ["UNKNOWN", "TO_TAKE_TASK", "COMPLETING_TASK", "TASK_COMPLETED", "TASK_NOT_STARTED", "TASK_ENDED", "PRIZE_TAKEN", "TASK_NOT_REWARD"], "x-ks-enum-detail": [{"name": "UNKNOWN", "value": "UNKNOWN"}, {"name": "TO_TAKE_TASK", "value": "TO_TAKE_TASK"}, {"name": "COMPLETING_TASK", "value": "COMPLETING_TASK"}, {"name": "TASK_COMPLETED", "value": "TASK_COMPLETED"}, {"name": "TASK_NOT_STARTED", "value": "TASK_NOT_STARTED"}, {"name": "TASK_ENDED", "value": "TASK_ENDED"}, {"name": "PRIZE_TAKEN", "value": "PRIZE_TAKEN"}, {"name": "TASK_NOT_REWARD", "value": "TASK_NOT_REWARD"}]}, "WishTravelPrizeType": {"type": "string", "description": "奖品类型", "enum": ["UNKNOWN", "DEFAULT_BLESS", "DEFAULT_LLCN", "LUCK_RUSH_CHANCE", "LLCN", "LLCH", "COUPON", "LLWDW_CARD", "LLCH_TRANSFER_CARD", "PROFILE_PENDANT", "COMMON_ZT_TASK_LLCN", "COMMON_ZT_TASK_LLCH", "FOLLOW_LLREWD_LLCN_TASK", "FOLLOW_LLREWD_LLCH_TASK", "FOLLOW_LLREWD_BLESS_TASK", "FOLLOW_LLREWD_RUSH_TASK", "WATCH_LIVE_LLREWD_LLCH_TASK", "WATCH_LIVE_LLREWD_LLCN_TASK", "WATCH_LIVE_LLREWD_RUSH_TASK", "INVOKE_APP_LLREWD_LLCN", "INVOKE_APP_LLREWD_LLCH", "INVOKE_APP_LLREWD_SHAKE", "TIME_LIMITED_ASSIST_DOUBLE_LLCN_TASK", "TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK", "TIME_LIMITED_COUNT_LLCN_TASK", "TIME_LIMITED_COUNT_LLCH_TASK", "TIME_LIMITED_INVITE_LLCN_TASK", "TIME_LIMITED_INVITE_LLCH_TASK", "TIME_LIMITED_INVITE_REFLUX_LLCN_TASK", "TIME_LIMITED_INVITE_REFLUX_LLCH_TASK", "TIME_LIMITED_PK_TASK", "TEAM_TASK", "AD_VIDEO", "AD_PHOTO", "FISSION_GOAT_DIVERSATION", "PHYSICAL_PRODUCT", "AD_STATIC_DIVERSION_POPUP", "ALWAYS_PULL_TASK", "RECRUIT_DIVERSION_DIRECT_PASS", "LOCAL_QUALIFICATION", "SHARE_LLCN_TASK", "PUSH_SWITCH_LLCN_TASK", "WATCH_VIDEO_LLCN_TASK", "RECRUIT_QUICK_HIRE_CARD_TRAIL", "RECRUIT_QUICK_HIRE_CARD_DISCOUNT", "RECRUIT_AI_BOOST_PACK_TRAIL", "IDEAT_BOOM_ORDER_CARD_TRIAL", "IDEAT_BOOM_ORDER_CARD_TIME_LIMIT_DISCOUNT", "DREAM_BUILDER_BOOM_ORDER_CARD_TRIAL", "DREAM_BUILDER_BOOM_ORDER_CARD_TIME_LIMIT_DISCOUNT"], "x-ks-enum-detail": [{"name": "UNKNOWN", "value": "UNKNOWN", "description": "未知"}, {"name": "DEFAULT_BLESS", "value": "DEFAULT_BLESS", "description": "兜底祝福语"}, {"name": "DEFAULT_LLCN", "value": "DEFAULT_LLCN", "description": "兜底金币奖励"}, {"name": "LUCK_RUSH_CHANCE", "value": "LUCK_RUSH_CHANCE", "description": "向前冲步数"}, {"name": "LLCN", "value": "LLCN", "description": "金币"}, {"name": "LLCH", "value": "LLCH", "description": "现金"}, {"name": "COUPON", "value": "COUPON", "description": "券"}, {"name": "LLWDW_CARD", "value": "LLWDW_CARD", "description": "提现卡"}, {"name": "LLCH_TRANSFER_CARD", "value": "LLCH_TRANSFER_CARD", "description": "现金打款"}, {"name": "PROFILE_PENDANT", "value": "PROFILE_PENDANT", "description": "头像挂件"}, {"name": "COMMON_ZT_TASK_LLCN", "value": "COMMON_ZT_TASK_LLCN", "description": "通用中台任务金币奖励"}, {"name": "COMMON_ZT_TASK_LLCH", "value": "COMMON_ZT_TASK_LLCH", "description": "通用中台任务现金奖励"}, {"name": "FOLLOW_LLREWD_LLCN_TASK", "value": "FOLLOW_LLREWD_LLCN_TASK", "description": "主播涨粉任务-奖励金币"}, {"name": "FOLLOW_LLREWD_LLCH_TASK", "value": "FOLLOW_LLREWD_LLCH_TASK", "description": "主播涨粉任务-奖励现金"}, {"name": "FOLLOW_LLREWD_BLESS_TASK", "value": "FOLLOW_LLREWD_BLESS_TASK", "description": "主播涨粉任务-祝福语"}, {"name": "FOLLOW_LLREWD_RUSH_TASK", "value": "FOLLOW_LLREWD_RUSH_TASK", "description": "主播涨粉任务-向前冲"}, {"name": "WATCH_LIVE_LLREWD_LLCH_TASK", "value": "WATCH_LIVE_LLREWD_LLCH_TASK", "description": "观看直播间-现金奖励"}, {"name": "WATCH_LIVE_LLREWD_LLCN_TASK", "value": "WATCH_LIVE_LLREWD_LLCN_TASK", "description": "观看直播间-金币奖励"}, {"name": "WATCH_LIVE_LLREWD_RUSH_TASK", "value": "WATCH_LIVE_LLREWD_RUSH_TASK", "description": "观看直播间-向前冲奖励"}, {"name": "INVOKE_APP_LLREWD_LLCN", "value": "INVOKE_APP_LLREWD_LLCN", "description": "双端互拉"}, {"name": "INVOKE_APP_LLREWD_LLCH", "value": "INVOKE_APP_LLREWD_LLCH"}, {"name": "INVOKE_APP_LLREWD_SHAKE", "value": "INVOKE_APP_LLREWD_SHAKE"}, {"name": "TIME_LIMITED_ASSIST_DOUBLE_LLCN_TASK", "value": "TIME_LIMITED_ASSIST_DOUBLE_LLCN_TASK", "description": "邀人金币翻倍-金币奖励"}, {"name": "TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK", "value": "TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK", "description": "邀人金币翻倍-金币奖励"}, {"name": "TIME_LIMITED_COUNT_LLCN_TASK", "value": "TIME_LIMITED_COUNT_LLCN_TASK", "description": "限时任务-完成一定数量的任务,发金币"}, {"name": "TIME_LIMITED_COUNT_LLCH_TASK", "value": "TIME_LIMITED_COUNT_LLCH_TASK", "description": "限时任务-完成一定数量的任务，发现金"}, {"name": "TIME_LIMITED_INVITE_LLCN_TASK", "value": "TIME_LIMITED_INVITE_LLCN_TASK", "description": "限时拉人任务-不限人群"}, {"name": "TIME_LIMITED_INVITE_LLCH_TASK", "value": "TIME_LIMITED_INVITE_LLCH_TASK", "description": "限时拉人任务-不限人群"}, {"name": "TIME_LIMITED_INVITE_REFLUX_LLCN_TASK", "value": "TIME_LIMITED_INVITE_REFLUX_LLCN_TASK", "description": "限时拉人任务-只限拉回"}, {"name": "TIME_LIMITED_INVITE_REFLUX_LLCH_TASK", "value": "TIME_LIMITED_INVITE_REFLUX_LLCH_TASK", "description": "限时拉人任务-只限拉回"}, {"name": "TIME_LIMITED_PK_TASK", "value": "TIME_LIMITED_PK_TASK", "description": "限时PK任务"}, {"name": "TEAM_TASK", "value": "TEAM_TASK", "description": "真人组队任务"}, {"name": "AD_VIDEO", "value": "AD_VIDEO"}, {"name": "AD_PHOTO", "value": "AD_PHOTO"}, {"name": "FISSION_GOAT_DIVERSATION", "value": "FISSION_GOAT_DIVERSATION", "description": "羊头导流"}, {"name": "PHYSICAL_PRODUCT", "value": "PHYSICAL_PRODUCT", "description": "实物商品"}, {"name": "AD_STATIC_DIVERSION_POPUP", "value": "AD_STATIC_DIVERSION_POPUP", "description": "商业化静态导流"}, {"name": "ALWAYS_PULL_TASK", "value": "ALWAYS_PULL_TASK", "description": "常驻拉人弹窗-不限人群"}, {"name": "RECRUIT_DIVERSION_DIRECT_PASS", "value": "RECRUIT_DIVERSION_DIRECT_PASS", "description": "招聘导流"}, {"name": "LOCAL_QUALIFICATION", "value": "LOCAL_QUALIFICATION", "description": "本地生活资格"}, {"name": "SHARE_LLCN_TASK", "value": "SHARE_LLCN_TASK", "description": "向前冲下发分享得金币任务"}, {"name": "PUSH_SWITCH_LLCN_TASK", "value": "PUSH_SWITCH_LLCN_TASK", "description": "向前冲下发打开push开关得金币任务"}, {"name": "WATCH_VIDEO_LLCN_TASK", "value": "WATCH_VIDEO_LLCN_TASK", "description": "向前冲下发观看视频得金币任务"}, {"name": "RECRUIT_QUICK_HIRE_CARD_TRAIL", "value": "RECRUIT_QUICK_HIRE_CARD_TRAIL", "description": "快聘快招卡7天免费试用"}, {"name": "RECRUIT_QUICK_HIRE_CARD_DISCOUNT", "value": "RECRUIT_QUICK_HIRE_CARD_DISCOUNT", "description": "快聘快招卡限时补贴优惠"}, {"name": "RECRUIT_AI_BOOST_PACK_TRAIL", "value": "RECRUIT_AI_BOOST_PACK_TRAIL", "description": "快聘AI加量包10天免费试用"}, {"name": "IDEAT_BOOM_ORDER_CARD_TRIAL", "value": "IDEAT_BOOM_ORDER_CARD_TRIAL", "description": "理想家爆单卡7天免费试用"}, {"name": "IDEAT_BOOM_ORDER_CARD_TIME_LIMIT_DISCOUNT", "value": "IDEAT_BOOM_ORDER_CARD_TIME_LIMIT_DISCOUNT", "description": "理想家爆单卡限时补贴优惠"}, {"name": "DREAM_BUILDER_BOOM_ORDER_CARD_TRIAL", "value": "DREAM_BUILDER_BOOM_ORDER_CARD_TRIAL", "description": "筑梦家爆单卡7天免费试用"}, {"name": "DREAM_BUILDER_BOOM_ORDER_CARD_TIME_LIMIT_DISCOUNT", "value": "DREAM_BUILDER_BOOM_ORDER_CARD_TIME_LIMIT_DISCOUNT", "description": "筑梦家爆单卡限时补贴优惠"}]}, "HugeSignInCancelPayView": {"type": "object", "properties": {"toast": {"type": "string"}}}, "ResultViewHugeSignInCancelPayView": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/HugeSignInCancelPayView"}, "timestamp": {"type": "integer", "format": "int64"}, "hostname": {"type": "string"}, "error_msg": {"type": "string"}}}, "HugeSignInLlrewdView": {"type": "object", "properties": {"linkType": {"$ref": "#/components/schemas/LinkTypeEnum"}, "linkUrl": {"type": "string", "description": "链接地址"}}, "description": "最终领奖view"}, "ResultViewHugeSignInLlrewdView": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/HugeSignInLlrewdView"}, "timestamp": {"type": "integer", "format": "int64"}, "hostname": {"type": "string"}, "error_msg": {"type": "string"}}}, "SignInCalendarRequest": {"type": "object", "properties": {"eventId": {"type": "string", "description": "日历ID"}}, "description": "保存日历请求参数"}, "AbstractLuckRushSudokuView": {"required": ["popupType"], "type": "object", "properties": {"popupType": {"$ref": "#/components/schemas/PopupType"}}, "description": "向前冲抽奖结果", "discriminator": {"propertyName": "popupType", "mapping": {"LS_LLCH_LLREWD": "#/components/schemas/LuckRushSudokuView", "LS_LLCN_LLREWD": "#/components/schemas/LuckRushSudokuView", "LS_COUPON_LLREWD": "#/components/schemas/LuckRushSudokuView", "LS_WITH_DRAW_CARD_LLREWD": "#/components/schemas/LuckRushSudokuView", "LS_LLCH_TRANSFER_CARD_LLREWD": "#/components/schemas/LuckRushSudokuView", "LS_PROFILE_PENDANT_LLREWD": "#/components/schemas/LuckRushSudokuView", "LS_BRANCH_VENUE_CARD_LLREWD": "#/components/schemas/LuckRushSudokuView", "LS_KOI_PROPS_LLREWD": "#/components/schemas/LuckRushSudokuView", "LS_SOCIAL_PROPS_LLREWD": "#/components/schemas/LuckRushSudokuView", "LS_COMMON_ZT_TASK_LLCN": "#/components/schemas/LuckRushSudokuView", "LS_COMMON_ZT_TASK_LLCH": "#/components/schemas/LuckRushSudokuView", "LS_FOLLOW_TASK_LLCN": "#/components/schemas/LuckRushSudokuView", "LS_FOLLOW_TASK_LLCH": "#/components/schemas/LuckRushSudokuView", "LS_FOLLOW_TASK_BLESS": "#/components/schemas/LuckRushSudokuView", "LS_WATCH_LIVE_TASK_LLCH": "#/components/schemas/LuckRushSudokuView", "LS_WATCH_LIVE_TASK_LLCN": "#/components/schemas/LuckRushSudokuView", "LS_RESERVE_WATCH_LIVE_LLCH": "#/components/schemas/LuckRushSudokuView", "LS_RESERVE_WATCH_LIVE_LLCN": "#/components/schemas/LuckRushSudokuView", "LS_RESERVE_LEEE_RAIN_LLCH": "#/components/schemas/LuckRushSudokuView", "LS_RESERVE_LEEE_RAIN_LLCN": "#/components/schemas/LuckRushSudokuView", "LS_INVOKE_APP_LLCH": "#/components/schemas/LuckRushSudokuView", "LS_INVOKE_APP_LLCN": "#/components/schemas/LuckRushSudokuView", "LS_HUG_SIGN_LLREWD": "#/components/schemas/LuckRushSudokuView", "LS_HUG_SIGN_SELECTED_GIFT": "#/components/schemas/LuckRushSudokuView", "LS_SOCIAL_LLMI_LLGE": "#/components/schemas/LuckRushSudokuView", "LS_RECRUIT_DIVERSION_DIRECT_PASS_LLREWD": "#/components/schemas/LuckRushSudokuView", "LS_FISSION_BUBBLE_DIVERSION_LLREWD": "#/components/schemas/LuckRushSudokuView", "LS_SOCIAL_POPUP_DIVERSION_LLREWD": "#/components/schemas/LuckRushSudokuView", "LS_TIME_LIMITED_COUNT_TASK_LLCN": "#/components/schemas/LuckRushSudokuView", "LS_TIME_LIMITED_COUNT_TASK_LLCH": "#/components/schemas/LuckRushSudokuView", "LS_TIME_LIMITED_ASSIST_TASK_LLCN": "#/components/schemas/LuckRushSudokuView", "LS_TIME_LIMITED_ASSIST_TASK_LLCH": "#/components/schemas/LuckRushSudokuView", "LS_TIME_LIMITED_REFLUX_ASSIST_TASK_LLCH": "#/components/schemas/LuckRushSudokuView", "LS_TIME_LIMITED_REFLUX_ASSIST_TASK_LLCN": "#/components/schemas/LuckRushSudokuView", "LS_TIME_TIME_LIMITED_PK_TASK": "#/components/schemas/LuckRushSudokuView", "LS_TIME_LIMITED_ASSIST_DOUBLE_LLCN": "#/components/schemas/LuckRushSudokuView", "LS_TIME_LIMITED_ASSIST_DOUBLE_LLCH": "#/components/schemas/LuckRushSudokuView", "LS_AD_VIDEO": "#/components/schemas/LuckRushSudokuView", "LS_AD_PHOTO": "#/components/schemas/LuckRushSudokuView", "LS_DEFAULT_BLESS": "#/components/schemas/LuckRushSudokuView", "LS_RECRUIT_STATIC_POPUP_LLREWD": "#/components/schemas/LuckRushSudokuView", "LS_DEFAULT_LLCN": "#/components/schemas/LuckRushSudokuView", "EVE_LS_FISSION_GOAT_DIVERSION_LLREWD": "#/components/schemas/LuckRushSudokuView", "EVE_BIG_BAG_DRAW": "#/components/schemas/LuckRushSudokuView", "LS_STATIC_POPUP_DIVERSION": "#/components/schemas/LuckRushSudokuView", "LS_RECRUIT_CAMPUS_TRIAL_CARD": "#/components/schemas/LuckRushSudokuView", "LS_RECRUIT_CAMPUS_TIME_LIMIT_CARD": "#/components/schemas/LuckRushSudokuView", "LS_RECRUIT_BOOM_ORDER_TRIAL_CARD": "#/components/schemas/LuckRushSudokuView", "LS_RECRUIT_BOOM_ORDER_TIME_LIMIT_CARD": "#/components/schemas/LuckRushSudokuView", "LS_RECRUIT_QUICK_CARD_TRIAL": "#/components/schemas/LuckRushSudokuView", "LS_RECRUIT_QUICK_CARD_DISCOUNT": "#/components/schemas/LuckRushSudokuView", "LS_RECRUIT_AI_BOOST_PACK_TRAIL": "#/components/schemas/LuckRushSudokuView", "LS_IDEAT_BOOM_ORDER_CARD_TRAIL": "#/components/schemas/LuckRushSudokuView", "LS_IDEAT_BOOM_ORDER_CARD_TIME_LIMIT_DISCOUNT": "#/components/schemas/LuckRushSudokuView", "LS_DREAM_BUILDER_BOOM_ORDER_CARD_TRIAL": "#/components/schemas/LuckRushSudokuView", "LS_DREAM_BUILDER_BOOM_ORDER_CARD_TIME_LIMIT_DISCOUNT": "#/components/schemas/LuckRushSudokuView", "TL_ALWAYS_PULL_TASK": "#/components/schemas/LuckRushSudokuView", "EVE_LS_RESERVE_LEEE_RAIN_SHAKE": "#/components/schemas/LuckRushSudokuView", "EVE_LS_INVOKE_APP_SHAKE": "#/components/schemas/LuckRushSudokuView", "EVE_LS_WATCH_LIVE_TASK_SHAKE": "#/components/schemas/LuckRushSudokuView", "EVE_LS_FOLLOW_TASK_SHAKE": "#/components/schemas/LuckRushSudokuView", "LS_DIVERSION_GUEST_TREE": "#/components/schemas/LuckRushSudokuView", "HUGE_SIGN_IN_PRODUCT": "#/components/schemas/HugeSignInProductPopupView", "HUGE_SIGN_IN_LLAWD": "#/components/schemas/HugeSignInLLawdFollowPopupView", "HUGE_SIGN_IN_STATION_NEW_COUPON_POPUP": "#/components/schemas/LuckRushSudokuView", "LS_SHARE_LLCN_TASK": "#/components/schemas/LuckRushSudokuView", "LS_PUSH_SWITCH_LLCN_TASK": "#/components/schemas/LuckRushSudokuView", "LS_WATCH_VIDEO_LLCN_TASK": "#/components/schemas/LuckRushSudokuView"}}, "oneOf": [{"$ref": "#/components/schemas/LuckRushSudokuView"}, {"$ref": "#/components/schemas/HugeSignInProductPopupView"}, {"$ref": "#/components/schemas/HugeSignInLLawdFollowPopupView"}]}, "AbstractPrizeDetailView": {"required": ["llpeType"], "type": "object", "properties": {"llpeType": {"$ref": "#/components/schemas/WishTravelPrizeType"}, "amount": {"type": "integer", "description": "奖励数量", "format": "int64"}, "displayAmount": {"type": "string", "description": "前端展示金额"}, "displayUnit": {"type": "string", "description": "前端展示单位"}}, "description": "奖品信息", "discriminator": {"propertyName": "llpeType", "mapping": {"DEFAULT_BLESS": "#/components/schemas/CommonStylePrizeView", "DEFAULT_LLCN": "#/components/schemas/CommonStylePrizeView", "LLCN": "#/components/schemas/CashPrizeView", "LLCH": "#/components/schemas/CoinPrizeView", "COUPON": "#/components/schemas/CouponPrizeDetailView", "LLWDW_CARD": "#/components/schemas/WithDrawCardView", "LLCH_TRANSFER_CARD": "#/components/schemas/DirectPayCardView", "PROFILE_PENDANT": "#/components/schemas/ProfilePendantView", "BRANCH_VENUE_CARD": "#/components/schemas/DiversionSfCardPrizeView", "BRANCH_VENUE_KOI_PROPS": "#/components/schemas/CommonStylePrizeView", "BRANCH_VENUE_SOCIAL_PROPS": "#/components/schemas/DiversionSocialPropsPrizeView", "COMMON_ZT_TASK_LLCH": "#/components/schemas/ZtTaskCommonCashPrizeView", "COMMON_ZT_TASK_LLCN": "#/components/schemas/ZtTaskCommonCoinPrizeView", "FOLLOW_LLREWD_LLCN_TASK": "#/components/schemas/TaskFollowPrizeView", "FOLLOW_LLREWD_LLCH_TASK": "#/components/schemas/TaskFollowPrizeView", "FOLLOW_LLREWD_BLESS_TASK": "#/components/schemas/TaskFollowPrizeView", "WATCH_LIVE_LLREWD_LLCH_TASK": "#/components/schemas/TaskWatchLivePrizeView", "WATCH_LIVE_LLREWD_LLCN_TASK": "#/components/schemas/TaskWatchLivePrizeView", "RESERVE_LEEE_RAIN_LLCN": "#/components/schemas/TaskReserveRedPackageRainPrizeView", "RESERVE_LEEE_RAIN_LLCH": "#/components/schemas/TaskReserveRedPackageRainPrizeView", "INVOKE_APP_LLREWD_LLCN": "#/components/schemas/TaskInvokeAppView", "INVOKE_APP_LLREWD_LLCH": "#/components/schemas/TaskInvokeAppView", "HUG_SIGN_DIVERSION": "#/components/schemas/DiversionHugeSignInPrizeView", "RECRUIT_DIVERSION_DIRECT_PASS": "#/components/schemas/DiversionRecruitJobDirectPassCardPrizeView", "FISSION_BUBBLE_DIVERSION": "#/components/schemas/CommonStylePrizeView", "SOCIAL_POPUP_DIVERSION": "#/components/schemas/DiversionSocialPopupPrizeView", "TIME_LIMITED_COUNT_LLCN_TASK": "#/components/schemas/TaskTimedLimitCountCoinPrizeView", "TIME_LIMITED_COUNT_LLCH_TASK": "#/components/schemas/TaskTimedLimitCountCashPrizeView", "TIME_LIMITED_INVITE_LLCN_TASK": "#/components/schemas/TaskTimedLimitInviteCoinPrizeView", "TIME_LIMITED_INVITE_LLCH_TASK": "#/components/schemas/TaskTimedLimitInviteCashPrizeView", "TIME_LIMITED_INVITE_REFLUX_LLCN_TASK": "#/components/schemas/TaskTimedLimitInviteRefluxCoinPrizeView", "TIME_LIMITED_INVITE_REFLUX_LLCH_TASK": "#/components/schemas/TaskTimedLimitInviteRefluxCashPrizeView", "TIME_LIMITED_PK_TASK": "#/components/schemas/PkTaskView", "TIME_LIMITED_ASSIST_DOUBLE_LLCN_TASK": "#/components/schemas/TaskTimedLimitAssistDoubleCoinPrizeView", "TIME_LIMITED_ASSIST_DOUBLE_LLCH_TASK": "#/components/schemas/TaskTimedLimitAssistDoubleCashPrizeView", "AD_VIDEO": "#/components/schemas/VideoPrizeDetailView", "AD_PHOTO": "#/components/schemas/AdPhotoPrizeDetailView", "SOCIAL_LLMI_LLGE_DIVERSION": "#/components/schemas/DiversionSocialMiniGamePrizeView", "RECRUIT_DIVERSION_STATIC_POPUP": "#/components/schemas/DiversionStaticPopUpPrizeView", "PHYSICAL_PRODUCT": "#/components/schemas/DiversionHugeSignInPrizeView", "LUCK_SHAKE_CHANCE": "#/components/schemas/DiversionHugeSignInPrizeView", "ALWAYS_PULL_TASK": "#/components/schemas/TaskAlwaysPullPrizeView", "RECRUIT_DIVERSION_CAMPUS_TRIAL_CARD": "#/components/schemas/CommonStylePrizeView", "RECRUIT_DIVERSION_CAMPUS_TIME_LIMIT_CARD": "#/components/schemas/CommonStylePrizeView", "RECRUIT_DIVERSION_BOOM_ORDER_TRIAL_CARD": "#/components/schemas/CommonStylePrizeView", "RECRUIT_DIVERSION_BOOM_ORDER_TIME_LIMIT_CARD": "#/components/schemas/CommonStylePrizeView", "AD_STATIC_DIVERSION_POPUP": "#/components/schemas/CommonStylePrizeView", "FISSION_GOAT_DIVERSATION": "#/components/schemas/CommonStylePrizeView", "INVOKE_APP_LLREWD_SHAKE": "#/components/schemas/TaskInvokeAppView", "RESERVE_LEEE_RAIN_SHAKE": "#/components/schemas/TaskReserveRedPackageRainPrizeView", "WATCH_LIVE_LLREWD_SHAKE_TASK": "#/components/schemas/TaskWatchLivePrizeView", "FOLLOW_LLREWD_SHAKE_TASK": "#/components/schemas/TaskFollowPrizeView", "SHARE_LLCN_TASK": "#/components/schemas/ZtTaskCommonCoinPrizeView", "PUSH_SWITCH_LLCN_TASK": "#/components/schemas/ZtTaskCommonCoinPrizeView", "WATCH_VIDEO_LLCN_TASK": "#/components/schemas/ZtTaskCommonCoinPrizeView"}}, "oneOf": [{"$ref": "#/components/schemas/CashPrizeView"}, {"$ref": "#/components/schemas/CoinPrizeView"}, {"$ref": "#/components/schemas/CouponPrizeDetailView"}, {"$ref": "#/components/schemas/WithDrawCardView"}, {"$ref": "#/components/schemas/DirectPayCardView"}, {"$ref": "#/components/schemas/ProfilePendantView"}, {"$ref": "#/components/schemas/DiversionSfCardPrizeView"}, {"$ref": "#/components/schemas/DiversionSocialPropsPrizeView"}, {"$ref": "#/components/schemas/ZtTaskCommonCashPrizeView"}, {"$ref": "#/components/schemas/ZtTaskCommonCoinPrizeView"}, {"$ref": "#/components/schemas/TaskFollowPrizeView"}, {"$ref": "#/components/schemas/TaskWatchLivePrizeView"}, {"$ref": "#/components/schemas/TaskReserveRedPackageRainPrizeView"}, {"$ref": "#/components/schemas/TaskInvokeAppView"}, {"$ref": "#/components/schemas/DiversionHugeSignInPrizeView"}, {"$ref": "#/components/schemas/CommonStylePrizeView"}, {"$ref": "#/components/schemas/DiversionSocialPopupPrizeView"}, {"$ref": "#/components/schemas/TaskTimedLimitCountCoinPrizeView"}, {"$ref": "#/components/schemas/TaskTimedLimitCountCashPrizeView"}, {"$ref": "#/components/schemas/TaskTimedLimitInviteCoinPrizeView"}, {"$ref": "#/components/schemas/TaskTimedLimitInviteCashPrizeView"}, {"$ref": "#/components/schemas/TaskTimedLimitInviteRefluxCoinPrizeView"}, {"$ref": "#/components/schemas/TaskTimedLimitInviteRefluxCashPrizeView"}, {"$ref": "#/components/schemas/PkTaskView"}, {"$ref": "#/components/schemas/TaskTimedLimitAssistDoubleCoinPrizeView"}, {"$ref": "#/components/schemas/TaskTimedLimitAssistDoubleCashPrizeView"}, {"$ref": "#/components/schemas/DiversionRecruitJobDirectPassCardPrizeView"}, {"$ref": "#/components/schemas/DiversionSocialMiniGamePrizeView"}, {"$ref": "#/components/schemas/DiversionStaticPopUpPrizeView"}, {"$ref": "#/components/schemas/VideoPrizeDetailView"}, {"$ref": "#/components/schemas/AdPhotoPrizeDetailView"}, {"$ref": "#/components/schemas/TaskAlwaysPullPrizeView"}]}, "AdPhotoPrizeDetailView": {"required": ["llpeType", "photoCdnUrl", "photoUrl"], "type": "object", "description": "商业化广告", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"photoUrl": {"type": "string", "description": "图片源地址"}, "photoCdnUrl": {"type": "string", "description": "图片CDN地址"}}}]}, "AddressInfo": {"type": "object", "properties": {"receiverName": {"type": "string"}, "phoneNumber": {"type": "string"}, "address": {"type": "string"}}}, "CashPrizeView": {"required": ["llpeType", "openSubTitle"], "type": "object", "description": "现金红包", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"openSubTitle": {"type": "string", "description": "打开状态下的标题"}}}]}, "ChessGridTaskDetailView": {"type": "object", "properties": {"commonTaskDetail": {"$ref": "#/components/schemas/CommonTaskDetail"}}, "description": "格子任务详情"}, "ChessMoveResultView": {"type": "object", "properties": {"progress": {"$ref": "#/components/schemas/ChessProgressView"}, "luckRushSudokuView": {"type": "array", "description": "向前冲奖励弹窗", "items": {"oneOf": [{"$ref": "#/components/schemas/HugeSignInLLawdFollowPopupView"}, {"$ref": "#/components/schemas/HugeSignInProductPopupView"}, {"$ref": "#/components/schemas/LuckRushSudokuView"}]}}}, "description": "向前冲结果"}, "ChessProgressView": {"required": ["currentStep", "expectTotalStep"], "type": "object", "properties": {"currentStep": {"type": "integer", "description": "已经走了多少步数", "format": "int32"}, "expectTotalStep": {"type": "integer", "description": "总共需要走多少步数", "format": "int32"}, "signed": {"type": "boolean", "description": "是否已完成签到"}, "currentGridTaskDetail": {"$ref": "#/components/schemas/ChessGridTaskDetailView"}, "currentTime": {"type": "integer", "description": "当前时间", "format": "int64"}, "lastStation": {"type": "boolean"}}, "description": "棋盘进度"}, "CoinPrizeView": {"required": ["bottomDesc", "llpeType"], "type": "object", "description": "金币", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"bottomDesc": {"type": "string", "description": "底部描述：约合现金xxx元"}}}]}, "CommonStylePrizeView": {"required": ["llpeType"], "type": "object", "description": "通用样式奖品", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"bottomDesc": {"type": "string", "description": "底部描述"}}}]}, "CommonTaskDetail": {"required": ["completeConditionAmount", "completeMaxTimes", "completedAmount", "completedTimes", "description", "displayText", "jumpLink", "jumpType", "subBizId", "take<PERSON><PERSON>us", "takeTime", "takeType", "taskContentId", "taskId", "taskPriority", "taskShowStyle", "taskStatus", "taskToken", "title"], "type": "object", "properties": {"taskId": {"type": "integer", "description": "任务id", "format": "int64"}, "title": {"type": "string", "description": "任务标题"}, "description": {"type": "string", "description": "任务描述"}, "iconUrls": {"type": "array", "description": "任务图标", "items": {"type": "string", "description": "任务图标"}}, "completeConditionAmount": {"type": "integer", "description": "阶段完成所需总量，多阶段累计?", "format": "int64"}, "completedAmount": {"type": "integer", "description": "阶段内已完成的总量，多阶段累计?", "format": "int64"}, "completeMaxTimes": {"type": "integer", "description": "阶段内最大完成次数?", "format": "int64"}, "completedTimes": {"type": "integer", "description": "阶段内已完成次数?", "format": "int64"}, "taskStatus": {"$ref": "#/components/schemas/TaskStatus"}, "displayText": {"type": "string", "description": "按钮展示文案"}, "jumpLink": {"type": "string", "description": "按钮跳转链接"}, "jumpType": {"type": "string", "description": "按钮跳转类型"}, "prizeName": {"type": "string", "description": "奖品名称"}, "prizeCount": {"type": "integer", "description": "奖品数量", "format": "int64"}, "prizeId": {"type": "integer", "description": "奖品id", "format": "int64"}, "extParams": {"type": "object", "additionalProperties": {"type": "string", "description": "扩展参数"}, "description": "扩展参数"}, "completeToastText": {"type": "string", "description": "任务完成toast"}, "timeLimitedStart": {"type": "integer", "description": "限时任务开始时间", "format": "int64"}, "timeLimitedEnd": {"type": "integer", "description": "限时任务结束时间", "format": "int64"}, "timeLimitExpireTime": {"type": "integer", "description": "限时任务过期时间", "format": "int64"}, "timeLimitedType": {"type": "integer", "description": "限时任务类型", "format": "int32"}, "takeTime": {"type": "integer", "description": "任务领取时间", "format": "int64"}, "delayQueryStatus": {"type": "boolean", "description": "delayQueryStatus"}, "widgetParam": {"type": "string", "description": "客户端拼装挂件所需参数"}, "taskShowStyle": {"type": "string", "description": "任务展示类型"}, "apkName": {"type": "string", "description": "apkName"}, "apkAddr": {"type": "string", "description": "apkAddr"}, "iosStoreAddr": {"type": "string", "description": "iosStoreAddr"}, "iosSchema": {"type": "string", "description": "iosSchema"}, "shareSubBiz": {"type": "string", "description": "分享subBiz"}, "unsupportedToast": {"type": "string", "description": "unsupportedToast"}, "taskPriority": {"type": "integer", "description": "任务优先级", "format": "int64"}, "taskContentId": {"type": "string", "description": "taskContentId"}, "taskToken": {"type": "string", "description": "taskId+userId encrypt"}, "subBizId": {"type": "integer", "description": "subBizId", "format": "int64"}, "takeType": {"type": "string", "description": "任务领取方式"}, "takeStatus": {"type": "string", "description": "任务领取状态"}, "assistUserInfo": {"type": "array", "description": "助力任务，助力者信息", "items": {"$ref": "#/components/schemas/CommonTaskUserInfo"}}, "relationChainInfo": {"type": "array", "description": "关系链用户信息", "items": {"$ref": "#/components/schemas/CommonTaskUserInfo"}}, "relationChainTypes": {"type": "array", "description": "关系链类型", "items": {"type": "string", "description": "关系链类型"}}, "prizeIconUrls": {"type": "array", "description": "奖品icon", "items": {"type": "string", "description": "奖品icon"}}, "reservationCalendarConfig": {"$ref": "#/components/schemas/CommonTaskCalendarInfo"}, "dynamicExtParam": {"type": "object", "additionalProperties": {"type": "string", "description": "业务扩展参数"}, "description": "业务扩展参数"}, "titleCornerText": {"type": "string", "description": "主标题角标文案"}, "descriptionLabel": {"type": "string", "description": "副标题标签"}, "taskPropertyKey": {"type": "string", "description": "任务自定义动态属性key"}, "backgroundColor": {"type": "string", "description": "任务底图背景颜色"}, "backgroundUrl": {"type": "string", "description": "任务底图背景url"}, "titleCornerUrl": {"type": "string", "description": "主标题角标图片url"}, "bannerUrl": {"type": "string", "description": "banner图片url"}, "cornerBubbleText": {"type": "string", "description": "角标气泡文案"}, "pendantIconUrl": {"type": "string", "description": "挂件配置项-icon图标url"}}, "description": "任务列表详情"}, "CouponPrizeDetailView": {"required": ["couponView", "llpeType", "showUnusedButton"], "type": "object", "description": "优惠券电商+商业化", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"couponView": {"$ref": "#/components/schemas/CouponView"}, "showUnusedButton": {"type": "boolean", "description": "券上是否展示立即使用的按钮"}}}]}, "CouponView": {"type": "object", "properties": {"displayType": {"$ref": "#/components/schemas/CouponViewDisplayType"}, "branding": {"type": "boolean"}, "addressInfo": {"$ref": "#/components/schemas/AddressInfo"}, "name": {"type": "string"}, "icon": {"type": "string"}, "desc": {"type": "string"}, "forwardUrl": {"type": "string"}, "forwardText": {"type": "string"}, "status": {"$ref": "#/components/schemas/CouponViewStatus"}, "value": {"type": "number", "format": "double"}, "valueText": {"type": "string"}, "valueDesc": {"type": "string"}, "image": {"type": "string"}, "expiredTime": {"type": "integer", "format": "int64"}, "validTime": {"type": "integer", "format": "int64"}, "pageTitle": {"type": "string"}, "partnerLogo": {"type": "string"}, "commercialTip": {"type": "string"}, "commercialLogo": {"type": "string"}, "blessing": {"type": "string"}, "recordId": {"type": "integer", "format": "int64"}, "prizeId": {"type": "integer", "format": "int64"}, "prizeType": {"$ref": "#/components/schemas/RewardPrizeTypeProto"}, "originalValue": {"type": "integer", "format": "int64"}, "shopCouponId": {"type": "string"}, "productItemId": {"type": "integer", "format": "int64"}, "commercialId": {"type": "string"}, "subBizId": {"type": "integer", "format": "int64"}, "rightId": {"type": "string"}, "count": {"type": "integer", "format": "int32"}, "cdKey": {"type": "string"}, "extraParam": {"type": "string"}}, "description": "具体券的样式"}, "CouponViewDisplayType": {"type": "string", "enum": ["unknown", "number", "discount", "image", "text", "special"], "x-ks-enum-detail": [{"name": "UNKNOWN", "value": "unknown"}, {"name": "NUMBER", "value": "number"}, {"name": "DISCOUNT", "value": "discount"}, {"name": "IMAGE", "value": "image"}, {"name": "TEXT", "value": "text"}, {"name": "SPECIAL", "value": "special"}]}, "CouponViewStatus": {"type": "string", "enum": ["invalid", "unused", "used", "expired", "view", "reviewing", "reviewFailure", "received", "unStarted", "granting", "compensated"], "x-ks-enum-detail": [{"name": "INVALID", "value": "invalid"}, {"name": "UNUSED", "value": "unused"}, {"name": "USED", "value": "used"}, {"name": "EXPIRED", "value": "expired"}, {"name": "VIEW", "value": "view"}, {"name": "REVIEWING", "value": "reviewing"}, {"name": "REVIEW_FAILURE", "value": "reviewFailure"}, {"name": "RECEIVED", "value": "received"}, {"name": "UN_STARTED", "value": "unStarted"}, {"name": "GRANTING", "value": "granting"}, {"name": "COMPENSATED", "value": "compensated"}]}, "DirectPayCardView": {"required": ["desc", "llpeType", "payAccountHeadUrl", "payAccountName", "payAccountType"], "type": "object", "description": "直接打款", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"desc": {"type": "string", "description": "描述: 现金打款0.50元"}, "payAccountName": {"type": "string", "description": "打款账号"}, "payAccountHeadUrl": {"type": "string", "description": "打款账号头像"}, "payAccountType": {"type": "string", "description": "打款账号类型，WECHAT/ALIPAY"}, "toast": {"type": "string", "description": "提示文案，只有在提现失败时会有"}, "paymentStatusDes": {"type": "string", "description": "支付状态的文案描述"}}}]}, "DiversionHugeSignInPrizeView": {"required": ["llpeType"], "type": "object", "description": "导流长签", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"llrewdDesc": {"type": "string", "description": "奖品描述：iphone15、茅台、黄金等好礼通通白拿"}, "llrewdName": {"type": "string", "description": "奖品描述：IPHONE15 1台。只有在选取了奖品时才会下发"}}}]}, "DiversionRecruitJobDirectPassCardPrizeView": {"required": ["address", "authorHeadUrl", "<PERSON><PERSON><PERSON>", "followOption", "followText", "jobDesc", "job<PERSON>ame", "llpeType", "redPackDesc", "salaryDesc", "salaryUnit"], "type": "object", "description": "快聘导流-职位直通卡", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"jobName": {"type": "string", "description": "职位名称"}, "salaryDesc": {"type": "string", "description": "薪水描述，5000-10000/月"}, "jobDesc": {"type": "array", "description": "职位的其他描述：年终奖 全勤奖 多劳多得", "items": {"type": "string", "description": "职位的其他描述：年终奖 全勤奖 多劳多得"}}, "authorName": {"type": "string", "description": "主播昵称"}, "authorHeadUrl": {"type": "string", "description": "主播头像"}, "redPackDesc": {"type": "string", "description": "奖励描述：已存入「新春招工会-我的奖品」"}, "followOption": {"type": "boolean", "description": "是否需要展示关注选项"}, "followText": {"type": "string", "description": "关注选项文案"}, "address": {"type": "string", "description": "地址"}, "salaryUnit": {"type": "string", "description": "薪水单位"}}}]}, "DiversionSfCardPrizeView": {"required": ["llpeType"], "type": "object", "description": "福卡", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}]}, "DiversionSocialMiniGamePrizeView": {"required": ["desc", "llpeType"], "type": "object", "description": "社交会场火崽崽小游戏", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"desc": {"type": "string", "description": "描述: 快手送你现金"}}}]}, "DiversionSocialPopupPrizeView": {"required": ["desc", "llpeType"], "type": "object", "description": "社交会场弹窗导流", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"desc": {"type": "string", "description": "描述: 快手送你现金"}}}]}, "DiversionSocialPropsPrizeView": {"required": ["llpeType"], "type": "object", "description": "社交会场道具导流", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}]}, "DiversionStaticPopUpPrizeView": {"required": ["llpeType"], "type": "object", "description": "快聘导流-静态导流卡", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}]}, "HugeSignInLLawdFollowPopupView": {"required": ["popupType", "sponsorLogo"], "type": "object", "description": "签到奖励弹窗, 包含有奖和无奖", "allOf": [{"$ref": "#/components/schemas/AbstractLuckRushSudokuView"}, {"type": "object", "properties": {"sponsorLogo": {"type": "string", "description": "赞助商logo"}, "sponsorText": {"type": "string", "description": "总冠名商Text"}, "title": {"type": "string", "description": "标题"}, "subTitle": {"type": "string", "description": "副标题"}, "continueSignInDays": {"type": "integer", "description": "连签天数", "format": "int32"}, "signInStationList": {"type": "array", "description": "站点7天列表", "items": {"$ref": "#/components/schemas/StationView"}}, "todayIndex": {"type": "integer", "description": "今天在列表里的index", "format": "int32"}, "llawdInfo": {"$ref": "#/components/schemas/LLawdView"}, "canGetLLawdDay": {"type": "integer", "description": "距离多少天可领大奖", "format": "int32"}, "hasStationLLawd": {"type": "boolean", "description": "是否存在站点奖励"}, "mainButton": {"$ref": "#/components/schemas/SummerCommonButtonView"}, "subButton": {"$ref": "#/components/schemas/SummerCommonButtonView"}}}]}, "HugeSignInProductPopupView": {"required": ["popupType"], "type": "object", "description": "签到成功选商品弹窗", "allOf": [{"$ref": "#/components/schemas/AbstractLuckRushSudokuView"}, {"type": "object", "properties": {"title": {"type": "string", "description": "标题"}, "roundId": {"type": "integer", "description": "轮次id", "format": "int32"}, "selectProductListView": {"$ref": "#/components/schemas/SignInSelectProductListView"}, "date": {"type": "string", "description": "弹窗日期"}}}]}, "InviteUserInfo": {"type": "object", "properties": {"userName": {"type": "string"}, "userHead": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}}, "description": "推荐的"}, "LLawdView": {"type": "object", "properties": {"amount": {"type": "integer", "description": "奖励数目", "format": "int64"}, "llawdText": {"type": "string", "description": "奖励主文案"}, "llawdSubText": {"type": "string", "description": "奖励副文案"}, "llawdDesc": {"type": "string", "description": "奖励描述"}, "llawdIcon": {"type": "string", "description": "奖励icon"}, "llawdName": {"type": "string", "description": "奖励名称"}, "productId": {"type": "integer", "description": "商品ID", "format": "int32"}, "couponId": {"type": "integer", "description": "券ID", "format": "int32"}}, "description": "奖励信息"}, "LuckRushSudokuView": {"required": ["popupType", "sponsorLogo"], "type": "object", "description": "抽奖弹窗", "allOf": [{"$ref": "#/components/schemas/AbstractLuckRushSudokuView"}, {"type": "object", "properties": {"sponsorLogo": {"type": "string", "description": "赞助商logo"}, "sponsorText": {"type": "string", "description": "总冠名商Text"}, "title": {"type": "string", "description": "主标题文案"}, "titleContext": {"$ref": "#/components/schemas/TitleContextVO"}, "subTitle": {"type": "string", "description": "副标题文案"}, "subTitleDesc": {"type": "string", "description": "副标题下面的描述文案"}, "mainButton": {"$ref": "#/components/schemas/SummerCommonButtonView"}, "subButton": {"$ref": "#/components/schemas/SummerCommonButtonView"}, "bottomButton": {"$ref": "#/components/schemas/SummerCommonButtonView"}, "blessing": {"type": "string", "description": " 祝福语"}, "icon": {"type": "string", "description": "奖品 icon"}, "showInTaskList": {"type": "boolean", "description": "是否在主页任务列表展示"}, "goldenStyle": {"type": "boolean", "description": "弹窗是否置金展示"}, "stationDayIndex": {"type": "integer", "description": "奖励对应站点天数", "format": "int32"}, "llpeDetail": {"type": "array", "description": "抽奖结果", "items": {"$ref": "#/components/schemas/AbstractPrizeDetailView"}}}}]}, "PkTaskView": {"required": ["forceRefreshMs", "highlight", "llpeType"], "type": "object", "description": "PK任务", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"highlight": {"type": "string", "description": "子标题的高亮部分"}, "forceRefreshMs": {"type": "integer", "description": "强制刷新时间", "format": "int64"}}}]}, "ProfilePendantView": {"required": ["bottomDesc", "llpeType"], "type": "object", "description": "P页头像挂件", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"bottomDesc": {"type": "string", "description": "底部描述：已存入钱包查看"}, "bottomButton": {"$ref": "#/components/schemas/SummerCommonButtonView"}}}]}, "ReservationCalendarVO": {"type": "object", "properties": {"rainTitle": {"type": "string", "description": "红包雨标题"}, "rainDesc": {"type": "string", "description": "红包雨描述"}, "calendarTitle": {"type": "string", "description": "加场红包雨的日历标题"}, "calendarRemark": {"type": "string", "description": "加场红包雨的日历备注"}, "startTime": {"type": "integer", "description": "红包雨的开始时间", "format": "int64"}, "endTime": {"type": "integer", "description": "红包雨的结束时间", "format": "int64"}, "duration": {"type": "integer", "description": "红包雨的持续时间，单位毫秒", "format": "int64"}, "durationType": {"type": "integer", "description": "持续的类型，1-每天，2-单日", "format": "int32"}}, "description": "红包雨场次"}, "ResultViewChessMoveResultView": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/ChessMoveResultView"}, "timestamp": {"type": "integer", "format": "int64"}, "hostname": {"type": "string"}, "error_msg": {"type": "string"}}}, "RewardPrizeTypeProto": {"type": "string", "enum": ["UNKNOWN_PRIZE_TYPE", "KS_COIN", "POINT", "CASH", "FANS_TOP_COUPON", "MERCHANT_COUPON", "CD_KEY", "PHYSICAL", "KUAI_MI", "MERCHANT_RANDOM_COUPON", "GOLD_COIN", "MOVIE_TICKET", "KSHELL", "GAME_GIFT_PACK", "VIRTUAL_SCORE", "STATIC_POINT", "DYNAMIC_POINT", "SF2021_PHYSICAL_STRENGTH", "SF2021_STATIC_CASH", "SF2021_DYNAMIC_CASH", "SF2021_STATIC_COIN", "SF2021_PK", "LOTTERY_EMPTY", "UNIFIED_MERCHANT_COUPON", "SF2021_RED_RAIN_COUPON", "SF2021_GAME_PACKET", "SF2021_COMMERCIAL_COUPON", "SF2021_BLESSING_CARD", "SF2021_CARD_DRAW_CHANCE", "UNICOM_VIDEO_PRODUCT", "SF2021_GROWING_RED_PACK", "SF2021_WIN_RATE", "SF2021_RETAINED_CASH", "LOTTERY_CHANCE", "SF2021_DRAW_CARD", "OLD_LOTTERY_CHANCE", "SF2021_RANDOM_CASH", "SF2021_NEW_WAN_YUAN", "SF2021_PPMT", "SF2021_CP_PLAN", "SF2021_FLYCHESS_TASK_POWER", "SF2021_GOLD_COUPON", "SF2021_CASH_WITHDRAW", "SF2021_GROWING_RED_PACK_V2", "RANDOM_PAY_GOLD_COIN", "NEBULA_GOLD_COIN", "RANDOM_NEBULA_GOLD_COIN", "CREATION_CAMP_TICKET", "ORCHARD_WATERDROP", "ORCHARD_SMALL_FERTILIZER", "ORCHARD_BIG_FERTILIZER", "RANDOM_ORCHARD_WATERDROP", "ZT_COUNT", "EXCHANGE_MERCHANT_COUPON", "GAME_CENTER_COUPON", "OLY2021_TASK", "OLY2021_MEDAL", "OLY2021_MAGIC_HAND", "OLY2021_FIGHT_TASK", "OLY2021_PK_START_UP_COIN", "OLY2021_PK_MILEAGE", "OLY2021_BEAN", "ACTIVITY_STATIC_CASH", "ACTIVITY_DYNAMIC_CASH", "RANDOM_KS_COIN", "ND2021_CRONUS", "ND2021_BOX_KEY", "ND2021_GOLD", "ND2021_MAGNET", "ND2021_TRIAD", "ND2021_CASH", "ND2021_PENDANT", "NO2021_DOUBLE_CARD", "ND2021_FLOAT_CASH", "ND2021_INVITATION", "USER_GROWTH_POINT", "ACTIVITY_CASH", "REWARD_TASK", "ACTIVITY_POINT", "GOLD_COIN_RAFFLE", "DRAW_CARD_CHANCE", "ZT_CARD", "JUMP2022_ACT_STEADY_JUMP", "JUMP2022_ACT_GAME_REVIVAL", "JUMP2022_ACT_MAMMON_BLESSING", "JUMP2022_ACT_MAMMON_APPENDAGE", "JUMP2022_ACT_ENERGY", "CNY2022_CARD", "CNY2022_DRAW_CARD_CHANCE", "KWAI_SHOP_MARKETING", "CARD2022_ACT_DRAW_CARD", "PHYSICAL_PRODUCT", "ZT_POINT", "WOG2022_MARIO_PHTSICAL_STRENGTH", "WOG2022_BINGO_GOLDEN_BEAN", "ID_MERCHANT_COUPON", "NEAR_BY_ACTIVITY", "CNY2022_STAFF_RED_PACKET", "CNY2022_MULTI_CARD", "CNY2022_SHELL", "CNY2022_CARD_DIVERSION", "ACTIVITY_CASH_V2", "LD2022_KKD", "LD2022_NICK_WIDGET", "LD2022_GAME_GIFT", "RECHARGE_GIVE_KS_COIN", "BACK_PACK_RESOURCE", "PAY_COIN", "LIVE_REVENUE", "SHORT_PLAY", "VENUE_POINT", "CUSTOM_PRIZE", "GAME_DUCK", "MINI_PROGRAM", "UPLIFT_CASH", "FILM_CARD", "CRONUS_PRODUCT", "ENCOURAGE_ACCOUNT", "OFFLINE_VIRTUAL", "CUSTOM_WALLET_PRIZE", "LBS_MARKETING_COUPON", "SPRING_KS_COIN", "RECHARGE_COUPON", "KMOVIE_VIP_COUPON", "M2U_VIP_COUPON", "FANSTOP_COUPON", "REWARD_PRIOR_TASK", "MOVIE_CARD", "ACTIVITY_COIN", "CASH_WITHDRAW", "PHYSICAL_MERCHANT_COUPON", "ACCOUNT_REWARD", "LBS_QUALIFICATION", "KEPLER_CASH", "KEPLER_COIN", "KEPLER_TASK", "CASH_PACKAGE", "COIN_PACKAGE", "UNRECOGNIZED"], "x-ks-enum-detail": [{"name": "UNKNOWN_PRIZE_TYPE", "value": "UNKNOWN_PRIZE_TYPE"}, {"name": "KS_COIN", "value": "KS_COIN"}, {"name": "POINT", "value": "POINT"}, {"name": "CASH", "value": "CASH"}, {"name": "FANS_TOP_COUPON", "value": "FANS_TOP_COUPON"}, {"name": "MERCHANT_COUPON", "value": "MERCHANT_COUPON"}, {"name": "CD_KEY", "value": "CD_KEY"}, {"name": "PHYSICAL", "value": "PHYSICAL"}, {"name": "KUAI_MI", "value": "KUAI_MI"}, {"name": "MERCHANT_RANDOM_COUPON", "value": "MERCHANT_RANDOM_COUPON"}, {"name": "GOLD_COIN", "value": "GOLD_COIN"}, {"name": "MOVIE_TICKET", "value": "MOVIE_TICKET"}, {"name": "KSHELL", "value": "KSHELL"}, {"name": "GAME_GIFT_PACK", "value": "GAME_GIFT_PACK"}, {"name": "VIRTUAL_SCORE", "value": "VIRTUAL_SCORE"}, {"name": "STATIC_POINT", "value": "STATIC_POINT"}, {"name": "DYNAMIC_POINT", "value": "DYNAMIC_POINT"}, {"name": "SF2021_PHYSICAL_STRENGTH", "value": "SF2021_PHYSICAL_STRENGTH"}, {"name": "SF2021_STATIC_CASH", "value": "SF2021_STATIC_CASH"}, {"name": "SF2021_DYNAMIC_CASH", "value": "SF2021_DYNAMIC_CASH"}, {"name": "SF2021_STATIC_COIN", "value": "SF2021_STATIC_COIN"}, {"name": "SF2021_PK", "value": "SF2021_PK"}, {"name": "LOTTERY_EMPTY", "value": "LOTTERY_EMPTY"}, {"name": "UNIFIED_MERCHANT_COUPON", "value": "UNIFIED_MERCHANT_COUPON"}, {"name": "SF2021_RED_RAIN_COUPON", "value": "SF2021_RED_RAIN_COUPON"}, {"name": "SF2021_GAME_PACKET", "value": "SF2021_GAME_PACKET"}, {"name": "SF2021_COMMERCIAL_COUPON", "value": "SF2021_COMMERCIAL_COUPON"}, {"name": "SF2021_BLESSING_CARD", "value": "SF2021_BLESSING_CARD"}, {"name": "SF2021_CARD_DRAW_CHANCE", "value": "SF2021_CARD_DRAW_CHANCE"}, {"name": "UNICOM_VIDEO_PRODUCT", "value": "UNICOM_VIDEO_PRODUCT"}, {"name": "SF2021_GROWING_RED_PACK", "value": "SF2021_GROWING_RED_PACK"}, {"name": "SF2021_WIN_RATE", "value": "SF2021_WIN_RATE"}, {"name": "SF2021_RETAINED_CASH", "value": "SF2021_RETAINED_CASH"}, {"name": "LOTTERY_CHANCE", "value": "LOTTERY_CHANCE"}, {"name": "SF2021_DRAW_CARD", "value": "SF2021_DRAW_CARD"}, {"name": "OLD_LOTTERY_CHANCE", "value": "OLD_LOTTERY_CHANCE"}, {"name": "SF2021_RANDOM_CASH", "value": "SF2021_RANDOM_CASH"}, {"name": "SF2021_NEW_WAN_YUAN", "value": "SF2021_NEW_WAN_YUAN"}, {"name": "SF2021_PPMT", "value": "SF2021_PPMT"}, {"name": "SF2021_CP_PLAN", "value": "SF2021_CP_PLAN"}, {"name": "SF2021_FLYCHESS_TASK_POWER", "value": "SF2021_FLYCHESS_TASK_POWER"}, {"name": "SF2021_GOLD_COUPON", "value": "SF2021_GOLD_COUPON"}, {"name": "SF2021_CASH_WITHDRAW", "value": "SF2021_CASH_WITHDRAW"}, {"name": "SF2021_GROWING_RED_PACK_V2", "value": "SF2021_GROWING_RED_PACK_V2"}, {"name": "RANDOM_PAY_GOLD_COIN", "value": "RANDOM_PAY_GOLD_COIN"}, {"name": "NEBULA_GOLD_COIN", "value": "NEBULA_GOLD_COIN"}, {"name": "RANDOM_NEBULA_GOLD_COIN", "value": "RANDOM_NEBULA_GOLD_COIN"}, {"name": "CREATION_CAMP_TICKET", "value": "CREATION_CAMP_TICKET"}, {"name": "ORCHARD_WATERDROP", "value": "ORCHARD_WATERDROP"}, {"name": "ORCHARD_SMALL_FERTILIZER", "value": "ORCHARD_SMALL_FERTILIZER"}, {"name": "ORCHARD_BIG_FERTILIZER", "value": "ORCHARD_BIG_FERTILIZER"}, {"name": "RANDOM_ORCHARD_WATERDROP", "value": "RANDOM_ORCHARD_WATERDROP"}, {"name": "ZT_COUNT", "value": "ZT_COUNT"}, {"name": "EXCHANGE_MERCHANT_COUPON", "value": "EXCHANGE_MERCHANT_COUPON"}, {"name": "GAME_CENTER_COUPON", "value": "GAME_CENTER_COUPON"}, {"name": "OLY2021_TASK", "value": "OLY2021_TASK"}, {"name": "OLY2021_MEDAL", "value": "OLY2021_MEDAL"}, {"name": "OLY2021_MAGIC_HAND", "value": "OLY2021_MAGIC_HAND"}, {"name": "OLY2021_FIGHT_TASK", "value": "OLY2021_FIGHT_TASK"}, {"name": "OLY2021_PK_START_UP_COIN", "value": "OLY2021_PK_START_UP_COIN"}, {"name": "OLY2021_PK_MILEAGE", "value": "OLY2021_PK_MILEAGE"}, {"name": "OLY2021_BEAN", "value": "OLY2021_BEAN"}, {"name": "ACTIVITY_STATIC_CASH", "value": "ACTIVITY_STATIC_CASH"}, {"name": "ACTIVITY_DYNAMIC_CASH", "value": "ACTIVITY_DYNAMIC_CASH"}, {"name": "RANDOM_KS_COIN", "value": "RANDOM_KS_COIN"}, {"name": "ND2021_CRONUS", "value": "ND2021_CRONUS"}, {"name": "ND2021_BOX_KEY", "value": "ND2021_BOX_KEY"}, {"name": "ND2021_GOLD", "value": "ND2021_GOLD"}, {"name": "ND2021_MAGNET", "value": "ND2021_MAGNET"}, {"name": "ND2021_TRIAD", "value": "ND2021_TRIAD"}, {"name": "ND2021_CASH", "value": "ND2021_CASH"}, {"name": "ND2021_PENDANT", "value": "ND2021_PENDANT"}, {"name": "NO2021_DOUBLE_CARD", "value": "NO2021_DOUBLE_CARD"}, {"name": "ND2021_FLOAT_CASH", "value": "ND2021_FLOAT_CASH"}, {"name": "ND2021_INVITATION", "value": "ND2021_INVITATION"}, {"name": "USER_GROWTH_POINT", "value": "USER_GROWTH_POINT"}, {"name": "ACTIVITY_CASH", "value": "ACTIVITY_CASH"}, {"name": "REWARD_TASK", "value": "REWARD_TASK"}, {"name": "ACTIVITY_POINT", "value": "ACTIVITY_POINT"}, {"name": "GOLD_COIN_RAFFLE", "value": "GOLD_COIN_RAFFLE"}, {"name": "DRAW_CARD_CHANCE", "value": "DRAW_CARD_CHANCE"}, {"name": "ZT_CARD", "value": "ZT_CARD"}, {"name": "JUMP2022_ACT_STEADY_JUMP", "value": "JUMP2022_ACT_STEADY_JUMP"}, {"name": "JUMP2022_ACT_GAME_REVIVAL", "value": "JUMP2022_ACT_GAME_REVIVAL"}, {"name": "JUMP2022_ACT_MAMMON_BLESSING", "value": "JUMP2022_ACT_MAMMON_BLESSING"}, {"name": "JUMP2022_ACT_MAMMON_APPENDAGE", "value": "JUMP2022_ACT_MAMMON_APPENDAGE"}, {"name": "JUMP2022_ACT_ENERGY", "value": "JUMP2022_ACT_ENERGY"}, {"name": "CNY2022_CARD", "value": "CNY2022_CARD"}, {"name": "CNY2022_DRAW_CARD_CHANCE", "value": "CNY2022_DRAW_CARD_CHANCE"}, {"name": "KWAI_SHOP_MARKETING", "value": "KWAI_SHOP_MARKETING"}, {"name": "CARD2022_ACT_DRAW_CARD", "value": "CARD2022_ACT_DRAW_CARD"}, {"name": "PHYSICAL_PRODUCT", "value": "PHYSICAL_PRODUCT"}, {"name": "ZT_POINT", "value": "ZT_POINT"}, {"name": "WOG2022_MARIO_PHTSICAL_STRENGTH", "value": "WOG2022_MARIO_PHTSICAL_STRENGTH"}, {"name": "WOG2022_BINGO_GOLDEN_BEAN", "value": "WOG2022_BINGO_GOLDEN_BEAN"}, {"name": "ID_MERCHANT_COUPON", "value": "ID_MERCHANT_COUPON"}, {"name": "NEAR_BY_ACTIVITY", "value": "NEAR_BY_ACTIVITY"}, {"name": "CNY2022_STAFF_RED_PACKET", "value": "CNY2022_STAFF_RED_PACKET"}, {"name": "CNY2022_MULTI_CARD", "value": "CNY2022_MULTI_CARD"}, {"name": "CNY2022_SHELL", "value": "CNY2022_SHELL"}, {"name": "CNY2022_CARD_DIVERSION", "value": "CNY2022_CARD_DIVERSION"}, {"name": "ACTIVITY_CASH_V2", "value": "ACTIVITY_CASH_V2"}, {"name": "LD2022_KKD", "value": "LD2022_KKD"}, {"name": "LD2022_NICK_WIDGET", "value": "LD2022_NICK_WIDGET"}, {"name": "LD2022_GAME_GIFT", "value": "LD2022_GAME_GIFT"}, {"name": "RECHARGE_GIVE_KS_COIN", "value": "RECHARGE_GIVE_KS_COIN"}, {"name": "BACK_PACK_RESOURCE", "value": "BACK_PACK_RESOURCE"}, {"name": "PAY_COIN", "value": "PAY_COIN"}, {"name": "LIVE_REVENUE", "value": "LIVE_REVENUE"}, {"name": "SHORT_PLAY", "value": "SHORT_PLAY"}, {"name": "VENUE_POINT", "value": "VENUE_POINT"}, {"name": "CUSTOM_PRIZE", "value": "CUSTOM_PRIZE"}, {"name": "GAME_DUCK", "value": "GAME_DUCK"}, {"name": "MINI_PROGRAM", "value": "MINI_PROGRAM"}, {"name": "UPLIFT_CASH", "value": "UPLIFT_CASH"}, {"name": "FILM_CARD", "value": "FILM_CARD"}, {"name": "CRONUS_PRODUCT", "value": "CRONUS_PRODUCT"}, {"name": "ENCOURAGE_ACCOUNT", "value": "ENCOURAGE_ACCOUNT"}, {"name": "OFFLINE_VIRTUAL", "value": "OFFLINE_VIRTUAL"}, {"name": "CUSTOM_WALLET_PRIZE", "value": "CUSTOM_WALLET_PRIZE"}, {"name": "LBS_MARKETING_COUPON", "value": "LBS_MARKETING_COUPON"}, {"name": "SPRING_KS_COIN", "value": "SPRING_KS_COIN"}, {"name": "RECHARGE_COUPON", "value": "RECHARGE_COUPON"}, {"name": "KMOVIE_VIP_COUPON", "value": "KMOVIE_VIP_COUPON"}, {"name": "M2U_VIP_COUPON", "value": "M2U_VIP_COUPON"}, {"name": "FANSTOP_COUPON", "value": "FANSTOP_COUPON"}, {"name": "REWARD_PRIOR_TASK", "value": "REWARD_PRIOR_TASK"}, {"name": "MOVIE_CARD", "value": "MOVIE_CARD"}, {"name": "ACTIVITY_COIN", "value": "ACTIVITY_COIN"}, {"name": "CASH_WITHDRAW", "value": "CASH_WITHDRAW"}, {"name": "PHYSICAL_MERCHANT_COUPON", "value": "PHYSICAL_MERCHANT_COUPON"}, {"name": "ACCOUNT_REWARD", "value": "ACCOUNT_REWARD"}, {"name": "LBS_QUALIFICATION", "value": "LBS_QUALIFICATION"}, {"name": "KEPLER_CASH", "value": "KEPLER_CASH"}, {"name": "KEPLER_COIN", "value": "KEPLER_COIN"}, {"name": "KEPLER_TASK", "value": "KEPLER_TASK"}, {"name": "CASH_PACKAGE", "value": "CASH_PACKAGE"}, {"name": "COIN_PACKAGE", "value": "COIN_PACKAGE"}, {"name": "UNRECOGNIZED", "value": "UNRECOGNIZED"}]}, "SignInSelectProductListView": {"type": "object", "properties": {"title": {"type": "string", "description": "标题"}, "subTitle": {"type": "string", "description": "副标题"}, "firstSelectTag": {"type": "string", "description": "标签: 官方承诺"}, "productList": {"type": "array", "description": "商品列表", "items": {"$ref": "#/components/schemas/SignInSelectProductView"}}, "selectedProductId": {"type": "integer", "description": "已选商品Id", "format": "int32"}, "freeChange": {"type": "boolean", "description": "当前是否可以免费换品"}}, "description": "选品商品列表view"}, "SignInSelectProductView": {"type": "object", "properties": {"templateId": {"type": "integer", "description": "模板ID", "format": "int32"}, "productId": {"type": "integer", "description": "商品ID", "format": "int32"}, "productName": {"type": "string", "description": "商品名称"}, "productIcon": {"type": "string", "description": "商品图片URL"}, "originLLpe": {"type": "string", "description": "参考价"}, "labels": {"type": "array", "description": "标签", "items": {"type": "string", "description": "标签"}}, "localLabels": {"type": "array", "description": "本地生活标签", "items": {"type": "string", "description": "本地生活标签"}}, "allSignDays": {"type": "integer", "description": "商品价格", "format": "int64"}, "llrewdType": {"type": "integer", "description": "商品类型", "format": "int32"}, "productChannel": {"type": "integer", "description": "商品通道，0:电商商品、金币、现金  1:本地生活商品", "format": "int32"}, "selectedNumText": {"type": "string", "description": "已选数量，12w人已选"}, "selectNumText": {"type": "string", "description": "可选数量，还剩12件"}, "limitProductRemainCount": {"type": "integer", "description": "还剩数量", "format": "int64"}, "limitNum": {"type": "integer", "description": "总数量", "format": "int32"}, "productRemainStatus": {"type": "integer", "description": "商品状态， 可选:0，不足: 1", "format": "int32"}}, "description": "签到商品信息"}, "StationView": {"type": "object", "properties": {"stationName": {"type": "string", "description": "站点名称"}, "stationIcon": {"type": "string", "description": "站点icon"}, "stationBlackIcon": {"type": "string", "description": "站点黑色icon"}}, "description": "站点列表"}, "TaskAlwaysPullPrizeView": {"required": ["llpeType", "needAssistCount", "taskExtra"], "type": "object", "description": "常驻拉人-不限人群", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"needAssistCount": {"type": "integer", "description": "需要拉多少人", "format": "int32"}, "taskExtra": {"type": "object", "description": "任务信息，透传中台信息"}}}]}, "TaskFollowPrizeView": {"required": ["authorHeadUrl", "authorId", "<PERSON><PERSON><PERSON>", "llpeType", "taskExtra"], "type": "object", "description": "关注任务", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"authorId": {"type": "integer", "description": "主播id", "format": "int64"}, "authorName": {"type": "string", "description": "主播名字"}, "authorHeadUrl": {"type": "string", "description": "主播头像"}, "taskExtra": {"type": "object", "description": "任务信息，透传中台信息"}}}]}, "TaskInvokeAppView": {"required": ["llpeType", "taskExtra"], "type": "object", "description": "双端互拉任务", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"taskExtra": {"type": "object", "description": "任务信息，透传中台信息"}}}]}, "TaskReserveRedPackageRainPrizeView": {"required": ["llpeType"], "type": "object", "description": "预约红包雨", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"rainInfo": {"type": "array", "description": "红包雨场次", "items": {"$ref": "#/components/schemas/ReservationCalendarVO"}}, "taskExtra": {"type": "object", "description": "任务信息"}}}]}, "TaskStatus": {"type": "string", "description": "任务状态", "enum": ["UNKNOWN", "TO_TAKE_TASK", "COMPLETING_TASK", "TASK_COMPLETED", "TASK_NOT_STARTED", "TASK_ENDED", "LLPE_TAKEN"], "x-ks-enum-detail": [{"name": "UNKNOWN", "value": "UNKNOWN"}, {"name": "TO_TAKE_TASK", "value": "TO_TAKE_TASK", "description": "任务待领取"}, {"name": "COMPLETING_TASK", "value": "COMPLETING_TASK", "description": "任务进行中"}, {"name": "TASK_COMPLETED", "value": "TASK_COMPLETED", "description": "任务已完成"}, {"name": "TASK_NOT_STARTED", "value": "TASK_NOT_STARTED", "description": "任务未开始（领取后未开始/限时未开始）"}, {"name": "TASK_ENDED", "value": "TASK_ENDED", "description": "任务已结束（领取后已结束/限时已结束）"}, {"name": "LLPE_TAKEN", "value": "LLPE_TAKEN", "description": "奖励已领取"}]}, "TaskTimedLimitAssistDoubleCashPrizeView": {"required": ["displayDoubleLlrewdAmount", "displayDoubleLlrewdUnit", "displayOriginalLlrewdAmount", "displayOriginalLlrewdUnit", "doubleLlrewdAmount", "doubleLlrewdDesc", "doubleLlrewdIcon", "expireTime", "llpeType", "needAssistCount", "originalLlrewdAmount", "originalLlrewdDesc", "originalLlrewdIcon", "taskExtra"], "type": "object", "description": "拉人翻倍-发现金", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"expireTime": {"type": "integer", "description": "倒计时结束时间,时间戳", "format": "int64"}, "originalLlrewdAmount": {"type": "integer", "description": "原始奖励信息: 100分", "format": "int64"}, "displayOriginalLlrewdAmount": {"type": "string", "description": "展示的原始奖励信息: 0.1 元"}, "displayOriginalLlrewdUnit": {"type": "string", "description": "展示的原始奖励信息单位"}, "originalLlrewdDesc": {"type": "string", "description": "原始奖励描述：约合现金1.00元"}, "originalLlrewdIcon": {"type": "string", "description": "原始奖励icon"}, "doubleLlrewdAmount": {"type": "integer", "description": "翻倍信息：10元", "format": "int64"}, "displayDoubleLlrewdAmount": {"type": "string", "description": "展示的翻倍信息: 0.1 元"}, "displayDoubleLlrewdUnit": {"type": "string", "description": "展示的翻倍信息单位"}, "doubleLlrewdDesc": {"type": "string", "description": "翻盘奖励描述：约合现金10.00元"}, "doubleLlrewdIcon": {"type": "string", "description": "翻倍奖励icon"}, "needAssistCount": {"type": "integer", "description": "需要拉多少人", "format": "int32"}, "taskExtra": {"type": "object", "description": "任务信息，透传中台信息"}}}]}, "TaskTimedLimitAssistDoubleCoinPrizeView": {"required": ["displayDoubleLlrewdAmount", "displayDoubleLlrewdUnit", "displayOriginalLlrewdAmount", "displayOriginalLlrewdUnit", "doubleLlrewdAmount", "doubleLlrewdDesc", "doubleLlrewdIcon", "expireTime", "llpeType", "needAssistCount", "originalLlrewdAmount", "originalLlrewdDesc", "originalLlrewdIcon", "taskExtra"], "type": "object", "description": "拉人翻倍-发金币", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"expireTime": {"type": "integer", "description": "倒计时结束时间,时间戳", "format": "int64"}, "originalLlrewdAmount": {"type": "integer", "description": "原始奖励信息: 100分", "format": "int64"}, "displayOriginalLlrewdAmount": {"type": "string", "description": "展示的原始奖励信息: 0.1 元"}, "displayOriginalLlrewdUnit": {"type": "string", "description": "展示的原始奖励信息单位"}, "originalLlrewdCorner": {"type": "string", "description": "原始奖励角标"}, "originalLlrewdDesc": {"type": "string", "description": "原始奖励描述：约合现金1.00元"}, "originalLlrewdIcon": {"type": "string", "description": "原始奖励icon"}, "doubleLlrewdAmount": {"type": "integer", "description": "翻倍信息：10元", "format": "int64"}, "displayDoubleLlrewdAmount": {"type": "string", "description": "展示的翻倍信息: 0.1 元"}, "displayDoubleLlrewdUnit": {"type": "string", "description": "展示的翻倍信息单位"}, "doubleLlrewdDesc": {"type": "string", "description": "翻盘奖励描述：约合现金10.00元"}, "doubleLlrewdIcon": {"type": "string", "description": "翻倍奖励icon"}, "doubleLlrewdCorner": {"type": "string", "description": "翻倍奖励角标"}, "needAssistCount": {"type": "integer", "description": "需要拉多少人", "format": "int32"}, "taskExtra": {"type": "object", "description": "任务信息，透传中台信息"}}}]}, "TaskTimedLimitCountCashPrizeView": {"required": ["expireTime", "llpeType"], "type": "object", "description": "限时任务-完成一定数量的任务-发现金", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"expireTime": {"type": "integer", "description": "倒计时结束时间,时间戳", "format": "int64"}}}]}, "TaskTimedLimitCountCoinPrizeView": {"required": ["expireTime", "llpeType"], "type": "object", "description": "限时任务-完成一定数量的任务-发金币", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"expireTime": {"type": "integer", "description": "倒计时结束时间,时间戳", "format": "int64"}}}]}, "TaskTimedLimitInviteCashPrizeView": {"required": ["desc", "expireTime", "llpeType", "needInviteCount", "shardSubBiz", "taskExtra"], "type": "object", "description": "限时拉人任务-不限人群", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"expireTime": {"type": "integer", "description": "倒计时结束时间,时间戳", "format": "int64"}, "desc": {"type": "string", "description": "描述：内完成扫码任务有效"}, "shardSubBiz": {"type": "string", "description": "分享 subBiz"}, "needInviteCount": {"type": "integer", "description": "需要邀请的用户数", "format": "int32"}, "taskExtra": {"type": "object", "description": "任务信息，透传中台信息"}}}]}, "TaskTimedLimitInviteCoinPrizeView": {"required": ["desc", "expireTime", "llpeType", "needInviteCount", "shardSubBiz", "taskExtra"], "type": "object", "description": "限时拉人任务-不限人群发金币", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"expireTime": {"type": "integer", "description": "倒计时结束时间,时间戳", "format": "int64"}, "desc": {"type": "string", "description": "描述：内完成扫码任务有效"}, "shardSubBiz": {"type": "string", "description": "分享 subBiz"}, "needInviteCount": {"type": "integer", "description": "需要邀请的用户数", "format": "int32"}, "taskExtra": {"type": "object", "description": "任务信息，透传中台信息"}}}]}, "TaskTimedLimitInviteRefluxCashPrizeView": {"required": ["desc", "expireTime", "llpeType", "shardSubBiz", "taskExtra"], "type": "object", "description": "限时拉人任务-拉回", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"expireTime": {"type": "integer", "description": "倒计时结束时间,时间戳", "format": "int64"}, "desc": {"type": "string", "description": "描述：内完成扫码任务有效"}, "shardSubBiz": {"type": "string", "description": "分享 subBiz"}, "recoUser": {"type": "array", "description": "推荐的", "items": {"$ref": "#/components/schemas/InviteUserInfo"}}, "needInviteCount": {"type": "integer", "description": "需要邀请的好友数量", "format": "int32"}, "taskExtra": {"type": "object", "description": "任务信息，透传中台信息"}}}]}, "TaskTimedLimitInviteRefluxCoinPrizeView": {"required": ["desc", "expireTime", "llpeType", "shardSubBiz", "taskExtra"], "type": "object", "description": "限时拉人任务-拉回发金币", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"expireTime": {"type": "integer", "description": "倒计时结束时间,时间戳", "format": "int64"}, "desc": {"type": "string", "description": "描述：内完成扫码任务有效"}, "shardSubBiz": {"type": "string", "description": "分享 subBiz"}, "recoUser": {"type": "array", "description": "推荐的好友信息", "items": {"$ref": "#/components/schemas/InviteUserInfo"}}, "needInviteCount": {"type": "integer", "description": "需要邀请的好友数量", "format": "int32"}, "taskExtra": {"type": "object", "description": "任务信息，透传中台信息"}}}]}, "TaskWatchLivePrizeView": {"required": ["llpeType", "taskExtra"], "type": "object", "description": "看直播任务-现金奖励", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"taskExtra": {"type": "object", "description": "任务信息，透传中台信息"}}}]}, "TitleContextVO": {"required": ["desc", "userList"], "type": "object", "properties": {"desc": {"type": "string", "description": "文案内容"}, "userList": {"type": "array", "description": "助力用户列表", "items": {"$ref": "#/components/schemas/UserBasicView"}}, "userCount": {"type": "integer", "format": "int64"}}, "description": "弹窗标题下的文案"}, "UserBasicView": {"type": "object", "properties": {"userName": {"type": "string", "description": "用户名"}, "headUrl": {"type": "string", "description": "用户头像"}}, "description": "用户基本信息"}, "VideoPrizeDetailView": {"required": ["coverUrl", "llpeType", "videoUrl"], "type": "object", "description": "商业化广告", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"videoUrl": {"type": "string", "description": "视频地址"}, "videoCdnUrl": {"type": "string", "description": "视频cdn调度地址"}, "coverUrl": {"type": "string", "description": "视频封面地址"}, "videoH264CdnUrl": {"type": "string", "description": "视频h264编码格式的cdn调度地址"}}}]}, "WithDrawCardView": {"required": ["llpeType", "llwdwDesc"], "type": "object", "description": "提现卡", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"llwdwDesc": {"type": "string", "description": "体现描述: 可提现至微信"}}}]}, "ZtTaskCommonCashPrizeView": {"required": ["llpeType", "taskExtra"], "type": "object", "description": "中台通用任务-现金奖励", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"taskExtra": {"type": "object", "description": "任务信息"}}}]}, "ZtTaskCommonCoinPrizeView": {"required": ["bottomDesc", "llpeType", "taskExtra"], "type": "object", "description": "中台通用任务-金币奖励", "allOf": [{"$ref": "#/components/schemas/AbstractPrizeDetailView"}, {"type": "object", "properties": {"bottomDesc": {"type": "string", "description": "底部描述：约合现金xxx元"}, "taskExtra": {"type": "object", "description": "任务信息"}}}]}, "ResultViewTeamPanelView": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/TeamPanelView"}, "timestamp": {"type": "integer", "format": "int64"}, "hostname": {"type": "string"}, "error_msg": {"type": "string"}}}, "TeamExitPopupView": {"type": "object", "properties": {"popupTitle": {"type": "string"}, "popupDesc": {"type": "string"}, "confirmButton": {"type": "string"}, "cancelButton": {"type": "string"}, "confirmPopupTitle": {"type": "string"}, "confirmPopupDesc": {"type": "string"}, "confirmPopupButton": {"type": "string"}, "sponsorLogo": {"type": "string"}, "sponsorText": {"type": "string"}}, "description": "退队弹窗"}, "TeamPanelView": {"required": ["desc", "teamId", "teamUser", "title"], "type": "object", "properties": {"title": {"type": "string", "description": "标题"}, "desc": {"type": "array", "description": "文案描述", "items": {"type": "string", "description": "文案描述"}}, "teamUser": {"type": "array", "description": "队伍用户信息", "items": {"$ref": "#/components/schemas/TeamUserView"}}, "chatId": {"type": "integer", "description": "群聊id,暂时不需要", "format": "int64"}, "taskFreeCardImg": {"type": "string", "description": "免打卡图片"}, "taskFreeCardNum": {"type": "integer", "description": "免打卡数量", "format": "int32"}, "taskFreeCardHighlightTitle": {"type": "string", "description": "免打卡高亮标题"}, "taskFreeCardDesc": {"type": "string", "description": "免打卡文案"}, "exitTeamPopup": {"$ref": "#/components/schemas/TeamExitPopupView"}, "showExitTeam": {"type": "boolean", "description": "是否展示退队"}, "teamBuildExpireTime": {"type": "integer", "description": "队伍组建过期时间", "format": "int64"}, "remainingTeamBuildTime": {"type": "integer", "description": "队伍组建剩余时间", "format": "int64"}, "teamBuildExpireDesc": {"type": "string", "description": "队伍组建过期文案"}, "togetherSignDays": {"type": "integer", "description": "共同打卡天数", "format": "int32"}, "togetherSignDesc": {"type": "string", "description": "共同打卡文案"}, "teamSignTotalDays": {"type": "integer", "description": "打卡总天数", "format": "int32"}, "teamSuccessMode": {"type": "boolean", "description": "队伍组建成功模式"}, "teamStatus": {"type": "integer", "description": "队伍状态", "format": "int32"}, "maxTeamUser": {"type": "integer", "description": "最大队伍人数", "format": "int32"}, "teamId": {"type": "integer", "description": "队伍id", "format": "int64"}, "freeCardImg": {"type": "string", "description": "免打卡图片"}, "freeCardListTitle": {"type": "string", "description": "免打卡弹窗标题"}, "freeCardListSubTitle": {"type": "string", "description": "免打卡弹窗副标题"}, "freeCardList": {"type": "array", "description": "免签卡列表", "items": {"$ref": "#/components/schemas/TeamUserFreeCardView"}}, "availableFreeCardNum": {"type": "integer", "description": "可用的免签卡数量", "format": "int32"}, "panelFreeCardDesc": {"type": "string", "description": "面板上的直通卡描述"}}}, "TeamUserFreeCardView": {"required": ["buttonType", "cardImg", "cardName", "validBeginTime", "validEndTime", "validTimeStr"], "type": "object", "properties": {"cardName": {"type": "string", "description": "卡片名称"}, "cardImg": {"type": "string", "description": "卡片图片"}, "validBeginTime": {"type": "integer", "description": "有效期开始时间", "format": "int64"}, "validEndTime": {"type": "integer", "description": "有效期结束时间", "format": "int64"}, "buttonType": {"type": "integer", "description": "按钮类型 1.去使用 2.明日可用 3.暂不可用 4.已使用 5.已过期", "format": "int32"}, "validTimeStr": {"type": "string", "description": "有效时间字符串"}}, "description": "免签卡列表"}, "TeamUserView": {"required": ["currentUser", "nick<PERSON><PERSON>", "userAvatar", "userId"], "type": "object", "properties": {"userId": {"type": "integer", "description": "用户id", "format": "int64"}, "nickName": {"type": "string", "description": "昵称"}, "userAvatar": {"type": "string", "description": "头像"}, "todaySign": {"type": "boolean", "description": "今日是否签到"}, "currentUser": {"type": "boolean", "description": "是否当前用户"}}, "description": "队伍用户信息"}, "ResultViewListTeamUserView": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TeamUserView"}}, "timestamp": {"type": "integer", "format": "int64"}, "hostname": {"type": "string"}, "error_msg": {"type": "string"}}}, "CommonTaskBubble": {"required": ["brandName", "icon", "linkTypeEnum", "taskId"], "type": "object", "properties": {"taskId": {"type": "integer", "description": "taskId", "format": "int64"}, "icon": {"type": "string", "description": "icon"}, "brandName": {"type": "string", "description": "冠名商名称"}, "linkTypeEnum": {"$ref": "#/components/schemas/LinkTypeEnum"}, "linkUrl": {"type": "string", "description": "链接url"}}, "description": "任务气泡id"}, "CommonTaskGroupConfig": {"required": ["groupName", "limitedChallengeTaskFlag", "title"], "type": "object", "properties": {"groupName": {"type": "string", "description": "任务组标识"}, "title": {"type": "string", "description": "任务组标题"}, "limitedChallengeTaskFlag": {"type": "boolean", "description": "任务组是否是限时挑战"}, "startTime": {"type": "integer", "description": "限时挑战任务开始时间", "format": "int64"}, "endTime": {"type": "integer", "description": "限时挑战任务结束时间", "format": "int64"}, "completeConditionAmount": {"type": "integer", "description": "需要完成的限时挑战任务数量总数", "format": "int32"}, "completeAmount": {"type": "integer", "description": "已完成的限时挑战任务数量", "format": "int32"}}, "description": "任务列表组信息"}, "ResultViewTaskListView": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/TaskListView"}, "timestamp": {"type": "integer", "format": "int64"}, "hostname": {"type": "string"}, "error_msg": {"type": "string"}}}, "TaskListView": {"required": ["canGetShakeCount", "limitedPeriod", "maxRefreshRetryTimes", "minRefreshCycleSec", "taskDetailList"], "type": "object", "properties": {"taskDetailList": {"type": "array", "description": "任务列表，所有任务", "items": {"$ref": "#/components/schemas/CommonTaskDetail"}}, "taskGroupConfigList": {"type": "array", "description": "任务组配置", "items": {"$ref": "#/components/schemas/CommonTaskGroupConfig"}}, "minRefreshCycleSec": {"type": "integer", "description": "最小刷新间隔(s) 前端轮询任务中台的状态接口", "format": "int64"}, "maxRefreshRetryTimes": {"type": "integer", "description": "最大重试次数", "format": "int64"}, "taskBubbleList": {"type": "array", "description": "气泡任务的taskId列表", "items": {"$ref": "#/components/schemas/CommonTaskBubble"}}, "canGetShakeCount": {"type": "boolean", "description": "是否还可以获得向前冲次数"}, "limitedPeriod": {"type": "boolean", "description": "是否处于限时挑战"}}, "description": "预热-任务列表+任务气泡"}, "ResultViewSummerShareInitView": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/SummerShareInitView"}, "timestamp": {"type": "integer", "format": "int64"}, "hostname": {"type": "string"}, "error_msg": {"type": "string"}}}, "SummerShareInitView": {"type": "object", "properties": {"shareCommonParam": {"type": "string", "description": "分享参数"}, "activityId": {"type": "string", "description": "活动id"}}, "description": "分享初始化接口返回参数"}, "InpushType": {"type": "string", "description": "类型", "enum": ["UNKNOWN", "ASSIST", "SHAKE_NUMBER_TASK", "RESUME_SUCCESS", "GOLD_COIN_TASK", "CASH_TASK", "TEAM", "RESUME_ASSIST", "GRID_TASK_UPDATE"], "x-ks-enum-detail": [{"name": "UNKNOWN", "value": "UNKNOWN", "description": "未知"}, {"name": "ASSIST", "value": "ASSIST", "description": "助力"}, {"name": "SHAKE_NUMBER_TASK", "value": "SHAKE_NUMBER_TASK", "description": "次数类任务"}, {"name": "RESUME_SUCCESS", "value": "RESUME_SUCCESS", "description": "续签成功"}, {"name": "GOLD_COIN_TASK", "value": "GOLD_COIN_TASK", "description": "金币类任务"}, {"name": "CASH_TASK", "value": "CASH_TASK", "description": "现金类任务"}, {"name": "TEAM", "value": "TEAM", "description": "队伍类"}, {"name": "RESUME_ASSIST", "value": "RESUME_ASSIST", "description": "续签助力"}, {"name": "GRID_TASK_UPDATE", "value": "GRID_TASK_UPDATE", "description": "格子任务进度更新"}]}, "ResultViewSummerWarmupInPushListView": {"required": ["data"], "type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/SummerWarmupInPushListView"}, "timestamp": {"type": "integer", "format": "int64"}, "hostname": {"type": "string"}, "error_msg": {"type": "string"}}}, "SummerWarmupButtonView": {"type": "object", "properties": {"linkType": {"$ref": "#/components/schemas/LinkTypeEnum"}, "linkText": {"type": "string", "description": "按钮文案"}, "linkSubText": {"type": "string", "description": "按钮子文案"}, "linkUrl": {"type": "string", "description": "跳转链接"}, "icon": {"type": "string", "description": "icon"}, "iconText": {"type": "string", "description": "icon文案"}, "anchorId": {"type": "integer", "description": "主播id", "format": "int64"}, "sudokuStartTime": {"type": "integer", "description": "抽奖开始时间", "format": "int64"}}, "description": "通用按钮"}, "SummerWarmupInPushListView": {"required": ["inpushList", "needRefreshPopup", "nextTimeMills"], "type": "object", "properties": {"inpushList": {"type": "array", "description": "inpush要展示的信息列表", "items": {"$ref": "#/components/schemas/SummerWarmupInPushView"}}, "nextTimeMills": {"type": "integer", "description": "下次调用inpush的时间间隔，毫秒级", "format": "int64"}, "needRefreshPopup": {"type": "boolean", "description": "是否需要刷新弹窗"}}, "description": "inpush列表信息"}, "SummerWarmupInPushView": {"required": ["assistedUserInfo", "buttonView", "content", "iconUrl", "inpushType", "title"], "type": "object", "properties": {"inpushType": {"$ref": "#/components/schemas/InpushType"}, "title": {"type": "string", "description": "标题信息"}, "content": {"type": "string", "description": "文案信息"}, "assistedUserInfo": {"type": "array", "description": "助力用户列表", "items": {"$ref": "#/components/schemas/SummerWarmupUserBasicView"}}, "iconUrl": {"type": "string", "description": "icon URL"}, "buttonView": {"$ref": "#/components/schemas/SummerWarmupButtonView"}}, "description": "inpush的信息"}, "SummerWarmupUserBasicView": {"type": "object", "properties": {"userName": {"type": "string", "description": "用户名"}, "headUrl": {"type": "string", "description": "用户头像"}}, "description": "用户信息"}, "HugeSignCalendarStationView": {"type": "object", "properties": {"stationName": {"type": "string", "description": "站点名称，未签到的站点不展示"}, "stationDayIndex": {"type": "integer", "description": "签到第几天", "format": "int32"}}, "description": "日历站点信息"}, "HugeSignInCalendarView": {"type": "object", "properties": {"title": {"type": "string", "description": "标题"}, "subTitle": {"type": "string", "description": "副标题"}, "historySignInStationList": {"type": "array", "description": "历史打卡站点", "items": {"$ref": "#/components/schemas/HugeSignCalendarStationView"}}, "currentSignInDay": {"type": "integer", "description": "当前签到天数", "format": "int32"}, "totalSignInDay": {"type": "integer", "description": "总共签到天数", "format": "int32"}, "showCalendarSwitch": {"type": "boolean", "description": "是否展示日历开关"}, "openRemind": {"type": "boolean", "description": "是否已打开提醒"}, "eventId": {"type": "string", "description": "日历eventId"}, "status": {"$ref": "#/components/schemas/HugeSignInStatus"}, "todaySigned": {"type": "boolean", "description": "今天是否签到"}}, "description": "签到日历"}, "HugeSignInStatus": {"type": "string", "description": "签到状态", "enum": ["UNKNOWN", "PROCESSING", "COMPLETED", "INTERCEPTED", "REWARDED", "REWARD_EXPIRE", "INTERCEPTED_EXPIRE", "ABANDON", "NOT_START"], "x-ks-enum-detail": [{"name": "UNKNOWN", "value": "UNKNOWN"}, {"name": "PROCESSING", "value": "PROCESSING"}, {"name": "COMPLETED", "value": "COMPLETED"}, {"name": "INTERCEPTED", "value": "INTERCEPTED"}, {"name": "REWARDED", "value": "REWARDED"}, {"name": "REWARD_EXPIRE", "value": "REWARD_EXPIRE"}, {"name": "INTERCEPTED_EXPIRE", "value": "INTERCEPTED_EXPIRE"}, {"name": "ABANDON", "value": "ABANDON"}, {"name": "NOT_START", "value": "NOT_START"}]}, "ResultViewHugeSignInCalendarView": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/HugeSignInCalendarView"}, "timestamp": {"type": "integer", "format": "int64"}, "hostname": {"type": "string"}, "error_msg": {"type": "string"}}}, "ResultViewSignInSelectProductListView": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/SignInSelectProductListView"}, "timestamp": {"type": "integer", "format": "int64"}, "hostname": {"type": "string"}, "error_msg": {"type": "string"}}}, "AbstractLLawdView": {"required": ["llpeType"], "type": "object", "properties": {"llpeType": {"$ref": "#/components/schemas/WishTravelPrizeType"}, "amount": {"type": "integer", "description": "奖励数量 金额,单位 分；金币，单位 个", "format": "int64"}, "displayAmount": {"type": "string", "description": "前端展示金额"}, "displayUnit": {"type": "string", "description": "前端展示单位"}}, "description": "奖品信息", "discriminator": {"propertyName": "llpeType", "mapping": {"LLCH": "#/components/schemas/LlchVO", "LLCN": "#/components/schemas/LlcnVO", "TIME_LIMITED_PK_TASK": "#/components/schemas/PkTaskLLawdVO", "LLWDW_CARD": "#/components/schemas/LlwdCardVO", "LLCH_TRANSFER_CARD": "#/components/schemas/DirectPayCardPopupView", "COUPON": "#/components/schemas/CouponLlPeDetailVO", "AD_VIDEO": "#/components/schemas/VideoLlpeDetailVO", "AD_PHOTO": "#/components/schemas/AdPhotoLLawdDetailView"}}, "oneOf": [{"$ref": "#/components/schemas/LlchVO"}, {"$ref": "#/components/schemas/LlcnVO"}, {"$ref": "#/components/schemas/PkTaskLLawdVO"}, {"$ref": "#/components/schemas/LlwdCardVO"}, {"$ref": "#/components/schemas/DirectPayCardPopupView"}, {"$ref": "#/components/schemas/CouponLlPeDetailVO"}, {"$ref": "#/components/schemas/VideoLlpeDetailVO"}, {"$ref": "#/components/schemas/AdPhotoLLawdDetailView"}]}, "AdPhotoLLawdDetailView": {"required": ["llpeType", "photoCdnUrl", "photoUrl"], "type": "object", "description": "商业化广告", "allOf": [{"$ref": "#/components/schemas/AbstractLLawdView"}, {"type": "object", "properties": {"photoUrl": {"type": "string", "description": "图片源地址"}, "photoCdnUrl": {"type": "string", "description": "图片CDN地址"}}}]}, "BeginnerGuidePopUpVO": {"required": ["popupType"], "type": "object", "description": "新手引导", "allOf": [{"$ref": "#/components/schemas/SummerPopupView"}]}, "BottomInfo": {"required": ["bottomDesc"], "type": "object", "properties": {"bottomDesc": {"type": "string", "description": "底部描述：已存入[我的钱包]钱包"}, "bottomButton": {"$ref": "#/components/schemas/SummerCommonButtonView"}}, "description": "底部按钮信息"}, "BroadCastInfoView": {"required": ["todaySignIn"], "type": "object", "properties": {"todaySignIn": {"type": "boolean", "description": "今日是否已签到"}, "nextLLrewdSignInDays": {"type": "integer", "description": "下一站有奖励的签到天数", "format": "int32"}, "llrewdName": {"type": "string", "description": "奖励名称"}}, "description": "播报区信息"}, "BuildingInfoView": {"type": "object", "properties": {"type": {"type": "string", "description": "建筑物类型"}, "name": {"type": "string", "description": "建筑物名称"}, "sponsorName": {"type": "string", "description": "赞助商名称"}, "iconUrl": {"type": "string", "description": "图片cdn链接"}, "linkUrl": {"type": "string", "description": "跳转链接"}, "linkType": {"type": "string", "description": "链接地址类型"}, "pos": {"type": "array", "description": "位置", "items": {"type": "integer", "description": "位置", "format": "int32"}}}, "description": "建筑物信息"}, "ChessMainButtonView": {"type": "object", "properties": {"rushCount": {"type": "integer", "description": "向前冲次数", "format": "int64"}, "reSignInCountdownTime": {"type": "integer", "description": "补签倒计时", "format": "int64"}, "stationDayIndex": {"type": "integer", "description": "站点天数(第几天)", "format": "int32"}, "hasValidSignFreeCard": {"type": "boolean", "description": "是否有有效的免签卡"}, "systemGivingRushChance": {"type": "integer", "description": "每日系统赠送次数", "format": "int32"}}, "description": "棋盘主按钮"}, "ChessStationView": {"required": ["llrewdGridLayout", "stationInfo"], "type": "object", "properties": {"stationInfo": {"$ref": "#/components/schemas/StationInfoView"}, "llrewdGridLayout": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/GridInfoView"}, "description": "奖励格子分布"}}, "description": "棋盘站点信息"}, "ChessboardView": {"required": ["initStationCount", "progress"], "type": "object", "properties": {"progress": {"$ref": "#/components/schemas/ChessProgressView"}, "buildingInfos": {"type": "array", "description": "建筑物信息", "items": {"$ref": "#/components/schemas/BuildingInfoView"}}, "stationHotInfoViews": {"$ref": "#/components/schemas/StationHotInfoView"}, "userInfo": {"$ref": "#/components/schemas/UserBasicView"}, "stationList": {"type": "array", "description": "站点信息列表", "items": {"$ref": "#/components/schemas/ChessStationView"}}, "currentStationIndex": {"type": "integer", "description": "当前站点下标", "format": "int32"}, "initStationCount": {"type": "integer", "description": "初始化站点数量", "format": "int32"}}, "description": "棋盘信息"}, "CouponLlPeDetailVO": {"required": ["couponView", "llpeType", "showUnusedButton"], "type": "object", "description": "优惠券电商+商业化", "allOf": [{"$ref": "#/components/schemas/AbstractLLawdView"}, {"type": "object", "properties": {"couponView": {"$ref": "#/components/schemas/CouponView"}, "showUnusedButton": {"type": "boolean", "description": "券上是否展示立即使用的按钮"}}}]}, "DirectPayCardPopupView": {"required": ["desc", "llpeType", "payAccountHeadUrl", "payAccountName", "payAccountType"], "type": "object", "description": "直接打款", "allOf": [{"$ref": "#/components/schemas/AbstractLLawdView"}, {"type": "object", "properties": {"desc": {"type": "string", "description": "描述: 现金打款0.50元"}, "payAccountName": {"type": "string", "description": "打款账号"}, "payAccountHeadUrl": {"type": "string", "description": "打款账号头像"}, "payAccountType": {"type": "string", "description": "打款账号类型，WECHAT/ALIPAY"}, "paymentStatusDes": {"type": "string", "description": "支付状态的文案描述"}}}]}, "GridInfoView": {"type": "object", "properties": {"gridIcon": {"type": "string", "description": "格子图标"}, "gridLocation": {"uniqueItems": true, "type": "array", "description": "格子位置", "items": {"type": "integer", "description": "格子位置", "format": "int32"}}, "gridTaskStatus": {"type": "array", "description": "任务格子状态", "items": {"$ref": "#/components/schemas/GridTaskStatus"}}}, "description": "格子信息"}, "GridTaskStatus": {"type": "number", "enum": [0, 1, 2], "x-ks-enum-detail": [{"name": "NOT_START", "value": 0, "description": "未开始"}, {"name": "IN_PROGRESS", "value": 1, "description": "进行中"}, {"name": "COMPLETED", "value": 2, "description": "已完成"}]}, "HomeAbTestConfigView": {"type": "object", "properties": {"beginnerGuideStrategy": {"type": "integer", "description": "ab实验新手引导策略配置", "format": "int32"}}, "description": "首页信息接口ab实验配置"}, "HomeAccountModel": {"type": "object", "properties": {"total": {"type": "string", "description": "总金额"}, "unit": {"type": "string", "description": "单位"}}, "description": "首页-账户信息"}, "HomeResourceDegradeConfigView": {"type": "object", "properties": {"cdnLevel": {"type": "integer", "description": "cdn降级等级", "format": "int64"}, "animationLevel": {"type": "integer", "description": "动效降级等级", "format": "int64"}, "videoLevel": {"type": "integer", "description": "视频降级等级", "format": "int64"}, "liveLevel": {"type": "integer", "description": "直播降级等级", "format": "int64"}, "audioLevel": {"type": "integer", "description": "音频降级等级", "format": "int64"}, "transparentVideoLevel": {"type": "integer", "description": "透明视频等级", "format": "int64"}}, "description": "首页信息接口资源降级配置"}, "HomeShowActivityView": {"type": "object", "properties": {"id": {"type": "integer", "description": "活动名", "format": "int64"}, "labelDesc": {"type": "string", "description": "活动描述"}, "title": {"type": "string", "description": "title"}, "icon": {"type": "string", "description": "icon"}, "iconText": {"type": "string", "description": "icon文案"}, "linkType": {"$ref": "#/components/schemas/LinkTypeEnum"}, "linkUrl": {"type": "string", "description": "跳转链接"}}, "description": "首页信息接口资源降级配置"}, "HomeTitleInfo": {"type": "object", "properties": {"logo": {"type": "string", "description": "商业化 logo"}, "title": {"type": "string", "description": "主标题"}, "subTitle": {"type": "string", "description": "副标题"}}, "description": "首页-冠名"}, "HomeView": {"required": ["mainButton"], "type": "object", "properties": {"signInHomeView": {"$ref": "#/components/schemas/SignInHomeView"}, "todaySigned": {"type": "boolean", "description": "今天是否已签到"}, "signInStatus": {"$ref": "#/components/schemas/HugeSignInStatus"}, "chessboard": {"$ref": "#/components/schemas/ChessboardView"}, "mainButton": {"$ref": "#/components/schemas/ChessMainButtonView"}, "titleInfo": {"$ref": "#/components/schemas/HomeTitleInfo"}, "accountModel": {"$ref": "#/components/schemas/HomeAccountModel"}, "homeUEConstantsConfig": {"type": "object", "description": "UE静态配置下发"}, "homeFEConstantsConfig": {"type": "object", "description": "FE静态配置下发"}, "homeResourceMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/HomeShowActivityView"}, "description": "资源位信息"}, "popList": {"type": "array", "description": "弹框信息,只有派发弹框,其他跟手弹框各个业务自行下发", "items": {"oneOf": [{"$ref": "#/components/schemas/SummerPopupView"}, {"$ref": "#/components/schemas/BeginnerGuidePopUpVO"}, {"$ref": "#/components/schemas/HugeSignChallengeFailurePopup"}, {"$ref": "#/components/schemas/HugeSignInInterruptResumePopup"}, {"$ref": "#/components/schemas/LLawdCoCaTaskPopupVO"}, {"$ref": "#/components/schemas/OldUserTipPopUpVO"}, {"$ref": "#/components/schemas/ShareVideoTaskPopupVO"}, {"$ref": "#/components/schemas/SignInPopUpVO"}, {"$ref": "#/components/schemas/TeamExitPopupVO"}, {"$ref": "#/components/schemas/TeamSignRewardVO"}, {"$ref": "#/components/schemas/TeamSuccessPopupVO"}, {"$ref": "#/components/schemas/TimeLimitTaskFailPopupVO"}]}}, "needSig3Path": {"type": "array", "description": "需要sig3鉴权的path", "items": {"type": "string", "description": "需要sig3鉴权的path"}}, "degradeConfigView": {"$ref": "#/components/schemas/HomeResourceDegradeConfigView"}, "teamEntryView": {"$ref": "#/components/schemas/TeamEntryView"}, "refreshTime": {"type": "integer", "description": "还剩多久前端进行刷新主接口，单位毫秒", "format": "int64"}, "abTestConfigView": {"$ref": "#/components/schemas/HomeAbTestConfigView"}, "chessStepSwitch": {"type": "boolean", "description": "去掉向前冲步数放量开关值"}}, "description": "首页信息view"}, "HotInfoView": {"type": "object", "properties": {"title": {"type": "string", "description": "标题"}, "linkUrl": {"type": "string", "description": "跳转链接"}}, "description": "当地热门视频"}, "HugeSignChallengeFailurePopup": {"required": ["popupType"], "type": "object", "allOf": [{"$ref": "#/components/schemas/SummerPopupView"}]}, "HugeSignInCalendarEventView": {"type": "object", "properties": {"title": {"type": "string"}, "note": {"type": "string"}, "url": {"type": "string"}, "startDay": {"type": "integer", "format": "int64"}, "endDay": {"type": "integer", "format": "int64"}, "type": {"type": "integer", "format": "int32"}}, "description": "写日历event信息"}, "LLawdCoCaTaskPopupVO": {"required": ["popupType"], "type": "object", "description": "限时拉人(新 回 活)成功 && 常驻拉新弹窗 && 限时挑战成功弹窗 && 观看直播成功弹窗 && 现金邀人再翻倍成功和失败弹窗 && 金币邀人再翻倍成功和失败弹窗 && 主播涨粉弹窗 && 预约红包雨弹窗 && 双端互拉任务 ", "allOf": [{"$ref": "#/components/schemas/SummerPopupView"}, {"type": "object", "properties": {"llrewdId": {"type": "integer", "description": "奖品id", "format": "int64"}, "titleContext": {"$ref": "#/components/schemas/TitleContextVO"}, "blessing": {"type": "string", "description": " 祝福语"}, "llpeDetail": {"type": "array", "description": "奖品结果，奖品类型应该为现金或者金币", "items": {"$ref": "#/components/schemas/AbstractLLawdView"}}, "userIdentity": {"$ref": "#/components/schemas/UserIdentityEnum"}, "bottomInfo": {"$ref": "#/components/schemas/BottomInfo"}}}]}, "LlchVO": {"required": ["llpeType", "openSubTitle"], "type": "object", "description": "现金红包", "allOf": [{"$ref": "#/components/schemas/AbstractLLawdView"}, {"type": "object", "properties": {"openSubTitle": {"type": "string", "description": "打开状态下的标题"}}}]}, "LlcnVO": {"required": ["bottomDesc", "llpeType", "openSubTitle"], "type": "object", "description": "金币", "allOf": [{"$ref": "#/components/schemas/AbstractLLawdView"}, {"type": "object", "properties": {"openSubTitle": {"type": "string", "description": "打开状态下的标题"}, "bottomDesc": {"type": "string", "description": "底部描述：约合现金xxx元"}}}]}, "LlwdCardVO": {"required": ["llpeType", "llwdwDesc"], "type": "object", "description": "提现卡", "allOf": [{"$ref": "#/components/schemas/AbstractLLawdView"}, {"type": "object", "properties": {"llwdwDesc": {"type": "string", "description": "提现描述: 可提现至微信"}}}]}, "OldUserTipPopUpVO": {"required": ["popupType"], "type": "object", "description": "存量用户提示弹窗", "allOf": [{"$ref": "#/components/schemas/SummerPopupView"}, {"type": "object", "properties": {"coinPopup": {"type": "boolean"}}}]}, "PkTaskLLawdVO": {"required": ["llpeType", "matchUserHead", "matchUserName", "pkDesc", "userHead", "userName"], "type": "object", "description": "PK任务奖励信息", "allOf": [{"$ref": "#/components/schemas/AbstractLLawdView"}, {"type": "object", "properties": {"pkDesc": {"type": "string", "description": "标题下的文案：别灰心，再接再厉有的是机会"}, "matchUserName": {"type": "string", "description": "匹配对手名字"}, "matchUserHead": {"type": "string", "description": "匹配对手头像"}, "matchUserCount": {"type": "integer", "description": "匹配对手完成的次数", "format": "int32"}, "userName": {"type": "string", "description": "用户昵称"}, "userHead": {"type": "string", "description": "用户头像"}, "userCount": {"type": "integer", "description": "当前用户完成的次数", "format": "int32"}}}]}, "ProgressAreaView": {"type": "object", "properties": {"stationInfos": {"type": "array", "description": "站点信息", "items": {"$ref": "#/components/schemas/StationInfoView"}}, "broadCastInfo": {"$ref": "#/components/schemas/BroadCastInfoView"}, "todayIndex": {"type": "integer", "description": "今日索引", "format": "int32"}, "todayStationInfo": {"$ref": "#/components/schemas/StationInfoView"}, "needSignInDays": {"type": "integer", "description": "需要打卡的天数", "format": "int32"}}, "description": "进度区信息"}, "ResultViewHomeView": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/HomeView"}, "timestamp": {"type": "integer", "format": "int64"}, "hostname": {"type": "string"}, "error_msg": {"type": "string"}}}, "ShareVideoTaskPopupVO": {"required": ["popupType"], "type": "object", "description": "魔表任务弹窗", "allOf": [{"$ref": "#/components/schemas/SummerPopupView"}, {"type": "object", "properties": {"icon": {"type": "string", "description": "魔表的icon"}, "shareVideoDesc": {"type": "string", "description": "魔表任务描述，eg: 发布作品记录此刻 摇红包次数+1"}, "iconDes": {"type": "string", "description": "魔表的文案"}}}]}, "SignInHomeView": {"type": "object", "properties": {"progressArea": {"$ref": "#/components/schemas/ProgressAreaView"}, "product": {"$ref": "#/components/schemas/SignInProductView"}, "eventId": {"type": "string", "description": "日历eventId"}, "calendarEventViewList": {"type": "array", "description": "写日历event信息", "items": {"$ref": "#/components/schemas/HugeSignInCalendarEventView"}}, "signInDays": {"type": "integer", "description": "签到天数", "format": "int32"}}, "description": "签到主页面信息"}, "SignInPopUpVO": {"required": ["expireTimeText", "llwrdName", "popupType", "productId"], "type": "object", "description": "签到弹窗", "allOf": [{"$ref": "#/components/schemas/SummerPopupView"}, {"type": "object", "properties": {"llwrdName": {"type": "string", "description": "奖品名称"}, "productId": {"type": "integer", "description": "商品ID", "format": "int32"}, "expireTimeText": {"type": "string", "description": "奖品过期时间文案"}}}]}, "SignInProductView": {"type": "object", "properties": {"productId": {"type": "integer", "description": "商品ID", "format": "int32"}, "productName": {"type": "string", "description": "商品名称"}, "productIcon": {"type": "string", "description": "商品icon"}, "signInDays": {"type": "integer", "description": "需要打卡天数", "format": "int32"}}, "description": "商品信息"}, "StationHotInfoView": {"type": "object", "properties": {"stationDesc": {"type": "string", "description": "站点描述"}, "hotInfos": {"type": "array", "description": "当地热门视频", "items": {"$ref": "#/components/schemas/HotInfoView"}}, "liveTime": {"type": "integer", "description": "视频播放时长", "format": "int64"}}, "description": "站点当地热门视频"}, "StationInfoView": {"type": "object", "properties": {"uniqueKey": {"type": "string", "description": "站点uniqueKey"}, "stationName": {"type": "string", "description": "站点名称"}, "stationIcon": {"type": "string", "description": "站点图标"}, "stationBlackIcon": {"type": "string", "description": "站点黑色图标"}, "chessStationIcon": {"type": "string", "description": "站点棋盘图标"}, "llrewdIcon": {"type": "string", "description": "站点奖励图标"}, "stationThemeKey": {"type": "string", "description": "站点主题素材key"}, "signed": {"type": "boolean", "description": "是否已签到"}, "stationDayIndex": {"type": "integer", "description": "站点天数(第几天)", "format": "int32"}, "stationTotalStep": {"type": "integer", "description": "站点总步数", "format": "int32"}, "stationBubbleIcon": {"type": "string", "description": "站点气泡icon"}, "normalBubbleText": {"type": "string", "description": "站点气泡文案"}, "stationBubbleText": {"type": "string", "description": "站点气泡文案"}, "tomorrowBubbleText": {"type": "string", "description": "明天站点气泡文案"}, "bubbleShowSeconds": {"type": "integer", "description": "站点气泡展示时长", "format": "int64"}, "gridSkinUrl": {"type": "string", "description": "站点格子皮肤链接"}, "gridSkinSponsor": {"type": "string", "description": "站点格子赞助商名称"}, "gridSkinLocation": {"type": "array", "description": "站点格子皮肤位置", "items": {"type": "integer", "description": "站点格子皮肤位置", "format": "int32"}}, "stationIconRewardText": {"type": "string", "description": "站点Icon奖励文案"}, "stationIconRewardTextColor": {"type": "string", "description": "站点文案颜色"}, "newLlrewdIcon": {"type": "string", "description": "新站点奖励icon"}}, "description": "站点信息"}, "SummerPopupView": {"required": ["popupType"], "type": "object", "properties": {"sponsorLogo": {"type": "string", "description": "总冠名商logo"}, "sponsorText": {"type": "string", "description": "总冠名商文案"}, "popupType": {"$ref": "#/components/schemas/PopupType"}, "title": {"type": "string", "description": "弹窗标题"}, "subTitle": {"type": "string", "description": "弹窗子标题"}, "userId": {"type": "integer", "description": "用户id", "format": "int64"}, "activityId": {"type": "string", "description": "活动id"}, "mainButton": {"oneOf": [{"$ref": "#/components/schemas/SummerCommonButtonView"}, {"$ref": "#/components/schemas/SummerDefaultButtonView"}, {"$ref": "#/components/schemas/SummerWarmupSocialButtonView"}]}, "subButton": {"oneOf": [{"$ref": "#/components/schemas/SummerCommonButtonView"}, {"$ref": "#/components/schemas/SummerDefaultButtonView"}, {"$ref": "#/components/schemas/SummerWarmupSocialButtonView"}]}, "taskId": {"type": "integer", "description": "任务id", "format": "int64"}, "sortScore": {"type": "integer", "format": "int64"}, "hashKeys": {"type": "array", "description": "要删除的hashKey", "items": {"type": "string", "description": "要删除的hashKey"}}, "desc": {"type": "string", "description": "弹窗描述"}, "icon": {"type": "string", "description": "弹窗icon"}}, "description": "弹窗vo", "discriminator": {"propertyName": "popupType", "mapping": {"BEGINNER_GUIDE": "#/components/schemas/BeginnerGuidePopUpVO", "SHARE_VIDEO_TASK": "#/components/schemas/ShareVideoTaskPopupVO", "PK_TASK_SUCCESS": "#/components/schemas/LLawdCoCaTaskPopupVO", "PK_TASK_FLAT": "#/components/schemas/LLawdCoCaTaskPopupVO", "TIME_ASSIST_TASK_SUCCESS": "#/components/schemas/LLawdCoCaTaskPopupVO", "TIME_ASSIST_RETURN_TASK_SUCCESS": "#/components/schemas/LLawdCoCaTaskPopupVO", "TIME_LIMIT_TASK_SUCCESS": "#/components/schemas/LLawdCoCaTaskPopupVO", "TIME_LIMIT_TASK_FAIL": "#/components/schemas/TimeLimitTaskFailPopupVO", "ASSIST_DOUBLE_LLCH_TASK_SUCCESS": "#/components/schemas/LLawdCoCaTaskPopupVO", "ASSIST_DOUBLE_LLCH_TASK_FAIL": "#/components/schemas/LLawdCoCaTaskPopupVO", "ASSIST_DOUBLE_LLCN_TASK_SUCCESS": "#/components/schemas/LLawdCoCaTaskPopupVO", "ASSIST_DOUBLE_LLCN_TASK_FAIL": "#/components/schemas/LLawdCoCaTaskPopupVO", "ALWAYS_PULL_NEW_TASK": "#/components/schemas/LLawdCoCaTaskPopupVO", "COMMON_LLCH_TASK": "#/components/schemas/LLawdCoCaTaskPopupVO", "RESERVATION_LLRP_RAIN_TASK": "#/components/schemas/LLawdCoCaTaskPopupVO", "TEAM_EXIT": "#/components/schemas/TeamExitPopupVO", "TEAM_SUCCESS": "#/components/schemas/TeamSuccessPopupVO", "TEAM_SIGN_REWARD": "#/components/schemas/TeamSignRewardVO", "HUGE_SIGN_IN_RESUME": "#/components/schemas/HugeSignInInterruptResumePopup", "HUGE_SIGN_IN_CHALLENGE_FAILURE": "#/components/schemas/HugeSignChallengeFailurePopup", "FINAL_RECEIVE_LLAWD": "#/components/schemas/SignInPopUpVO", "FINAL_RECEIVE_EXPIRE": "#/components/schemas/SignInPopUpVO", "OLD_USER_TIP": "#/components/schemas/OldUserTipPopUpVO"}}, "oneOf": [{"$ref": "#/components/schemas/BeginnerGuidePopUpVO"}, {"$ref": "#/components/schemas/ShareVideoTaskPopupVO"}, {"$ref": "#/components/schemas/LLawdCoCaTaskPopupVO"}, {"$ref": "#/components/schemas/TimeLimitTaskFailPopupVO"}, {"$ref": "#/components/schemas/TeamSuccessPopupVO"}, {"$ref": "#/components/schemas/TeamExitPopupVO"}, {"$ref": "#/components/schemas/TeamSignRewardVO"}, {"$ref": "#/components/schemas/HugeSignInInterruptResumePopup"}, {"$ref": "#/components/schemas/HugeSignChallengeFailurePopup"}, {"$ref": "#/components/schemas/SignInPopUpVO"}, {"$ref": "#/components/schemas/OldUserTipPopUpVO"}]}, "TeamEntryView": {"required": ["entryStatus", "newEntryStatus", "newShowPop", "number", "popType", "showPop"], "type": "object", "properties": {"entryStatus": {"type": "integer", "description": "入口状态 1.去组队 2已打卡 3未打卡 4.免签卡", "format": "int32"}, "teamUser": {"type": "array", "description": "组队用户列表", "items": {"$ref": "#/components/schemas/TeamUserView"}}, "freeCardImg": {"type": "string", "description": "免打卡图片"}, "freeCardListTitle": {"type": "string", "description": "免打卡弹窗标题"}, "freeCardListSubTitle": {"type": "string", "description": "免打卡弹窗副标题"}, "freeCardList": {"type": "array", "description": "免签卡列表", "items": {"$ref": "#/components/schemas/TeamUserFreeCardView"}}, "showPop": {"type": "boolean", "description": "是否展示气泡"}, "popType": {"type": "integer", "description": "气泡类型 1.组队引导 2.未打卡提醒", "format": "int32"}, "number": {"type": "integer", "description": "数量", "format": "int32"}, "teamIslandText": {"type": "string", "description": "组队灵动岛展示文案"}, "teamId": {"type": "integer", "description": "队伍id", "format": "int64"}, "teamStatus": {"type": "integer", "description": "队伍状态", "format": "int32"}, "newEntryStatus": {"type": "integer", "description": "入口状态 1.去组队 2已打卡 3未打卡 4.免签卡", "format": "int32"}, "newShowPop": {"type": "boolean", "description": "是否展示气泡"}, "exitTeamGuide": {"type": "boolean", "description": "是否出退队引导弹窗"}}, "description": "组队资源位信息"}, "TeamExitPopupVO": {"required": ["popupType", "type"], "type": "object", "allOf": [{"$ref": "#/components/schemas/SummerPopupView"}, {"type": "object", "properties": {"reasonType": {"$ref": "#/components/schemas/TeamExitReasonType"}, "type": {"type": "integer", "description": "类型 1.弹toast并且拉起面板 2.展示弹窗", "format": "int32"}, "toast": {"type": "string", "description": "toast"}, "message": {"type": "string", "description": "message描述"}}}]}, "TeamExitReasonType": {"type": "string", "description": "退出理由", "enum": ["NOT", "EXIT_TEAM", "BREAK_SIGN", "MATCH_EXPIRE", "REWARD_SUCCESS", "CHANGE_LONG_SIGN_REWARD", "ACCOUNT_RESET", "ACQUIRE_LONG_SIGN_REWARD", "LONG_SIGN_REWARD_EXPIRE"], "x-ks-enum-detail": [{"name": "NOT", "value": "NOT"}, {"name": "EXIT_TEAM", "value": "EXIT_TEAM"}, {"name": "BREAK_SIGN", "value": "BREAK_SIGN"}, {"name": "MATCH_EXPIRE", "value": "MATCH_EXPIRE"}, {"name": "REWARD_SUCCESS", "value": "REWARD_SUCCESS"}, {"name": "CHANGE_LONG_SIGN_REWARD", "value": "CHANGE_LONG_SIGN_REWARD"}, {"name": "ACCOUNT_RESET", "value": "ACCOUNT_RESET"}, {"name": "ACQUIRE_LONG_SIGN_REWARD", "value": "ACQUIRE_LONG_SIGN_REWARD"}, {"name": "LONG_SIGN_REWARD_EXPIRE", "value": "LONG_SIGN_REWARD_EXPIRE"}]}, "TeamSignRewardVO": {"required": ["popupType", "taskFreeCardNum"], "type": "object", "allOf": [{"$ref": "#/components/schemas/SummerPopupView"}, {"type": "object", "properties": {"taskFreeCardNum": {"type": "integer", "description": "直通卡数量", "format": "int32"}}}]}, "TeamSuccessPopupVO": {"required": ["animationDesc", "animationTitle", "popupType", "taskFreeCardNum"], "type": "object", "allOf": [{"$ref": "#/components/schemas/SummerPopupView"}, {"type": "object", "properties": {"animationTitle": {"type": "string", "description": "动画标题"}, "animationDesc": {"type": "string", "description": "动画描述"}, "taskFreeCardNum": {"type": "integer", "description": "直通卡数量", "format": "int32"}}}]}, "TimeLimitTaskFailPopupVO": {"required": ["popupType"], "type": "object", "description": "限时任务失败弹窗", "allOf": [{"$ref": "#/components/schemas/SummerPopupView"}, {"type": "object", "properties": {"blessing": {"type": "string", "description": " 祝福语"}, "taskProgress": {"type": "array", "description": " 任务进度详情", "items": {"$ref": "#/components/schemas/TimeLimitTaskProgress"}}, "failToast": {"type": "string", "description": " 任务未完成的toast"}, "count": {"type": "integer", "description": "限时任务数量", "format": "int32"}}}]}, "TimeLimitTaskProgress": {"type": "object", "properties": {"taskName": {"type": "string", "description": "任务名称"}, "taskStatus": {"type": "integer", "description": "任务状态, 0 是未完成 1 是完成", "format": "int32"}}, "description": "限时任务失败时，任务进度详情"}, "UserIdentityEnum": {"type": "string", "description": "用户的身份", "enum": ["FIRST_ACTIVE", "NEW", "REFLUX", "ACTIVE"], "x-ks-enum-detail": [{"name": "FIRST_ACTIVE", "value": "FIRST_ACTIVE", "description": "首活"}, {"name": "NEW", "value": "NEW", "description": "新"}, {"name": "REFLUX", "value": "REFLUX", "description": "回"}, {"name": "ACTIVE", "value": "ACTIVE", "description": "活"}]}, "VideoLlpeDetailVO": {"required": ["coverUrl", "llpeType", "videoUrl"], "type": "object", "description": "优惠券电商+商业化视频", "allOf": [{"$ref": "#/components/schemas/AbstractLLawdView"}, {"type": "object", "properties": {"videoUrl": {"type": "string", "description": "视频地址"}, "videoCdnUrl": {"type": "string", "description": "视频cdn调度地址"}, "coverUrl": {"type": "string", "description": "视频封面地址"}, "videoH264CdnUrl": {"type": "string", "description": "视频h264编码格式的cdn调度地址"}}}]}}}}