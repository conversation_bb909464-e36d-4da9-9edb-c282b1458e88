import { isLowDevice as isLD } from '@pet/yau.core';
import { createGlobalState, watchDebounced } from '@vueuse/core';
import { computed, ref, watch } from 'vue';
import { uaGetInfo } from './ua';
import useCaptureDebugLog from '@pet/yau.logger';
import { sendEvent } from '@pet/yau.radar';

import {
    handleDeviceTemperatureGradeChange,
    TemperatureGrade,
    type TemperatureGradeChangeCallback,
} from '@aug/device-energy-governance';

/**
 *  动效降级等级 0不降级，3全降级
 */
export type LevelType = 0 | 1 | 2 | 3;

/**
 * 等级LevelEnum
 */
export enum LevelEnum {
    L0 = 0,
    L1 = 1,
    L2 = 2,
    L3 = 3,
}

const uaInfo = uaGetInfo();
function transInd(uaValue: string | undefined) {
    return +(uaValue ?? '0') > 0;
}

/** 通过 ua，query，env 判断是否是低端机设备，低端机默认降级L3 */
export const isLowDevice = isLD || transInd(uaInfo.islp) || transInd(uaInfo.icfo) || transInd(uaInfo.islm);

export const isLowBattery = transInd(uaInfo.islb);

const { log } = useCaptureDebugLog('downgrade-level');

export const useDowngradeLevel = createGlobalState(() => {
    /** 动效降级等级，默认L3，拿到下发数据后才修改 */
    const effectLevel = ref<LevelType>(LevelEnum.L3);
    /** 视频降级等级，默认L0，不降级 */
    const videoLevel = ref<LevelType>(LevelEnum.L0);
    /** 音频降级等级，默认L3，拿到下发数据后才修改 */
    const audioLevel = ref<LevelType>(LevelEnum.L3);
    /** 透明视频降级等级, 默认L0, 完全降级, 拿到下发数据后才修改 */
    const transparentVideoLevel = ref<LevelType>(LevelEnum.L0);

    /** 发热等级（和降级等级可以1对1跟得上） */
    const temperatureGrade = ref<TemperatureGrade>();

    handleDeviceTemperatureGradeChange({
        cb: (res: TemperatureGradeChangeCallback) => {
            log('temperature grade', res.temperatureGrade);
            if (temperatureGrade.value === undefined) {
                temperatureGrade.value = res.temperatureGrade ?? TemperatureGrade.Normal;
            }
        },
    });

    /** 发热等级和当前降级等级取最高 */
    const currentEffectLevel = computed(() => {
        // 如果 temperatureGrade.value 为 null 或 undefined，则默认为 TemperatureGrade.Normal
        const tempGrade = temperatureGrade.value ?? TemperatureGrade.Normal;

        // 新逻辑: 如果 temperatureGrade 是 Normal 或 LightHot，则它不生效
        // 用户需求: 表示温度的temperatureGrade 在 TemperatureGrade.Normal 和 LightHot 这两个等级不生效
        if (
            tempGrade === TemperatureGrade.Normal ||
            tempGrade === TemperatureGrade.LightHot
        ) {
            return effectLevel.value;
        }
        else {
            // 对于其他温度等级，应用原始逻辑
            return Math.max(tempGrade, effectLevel.value);
        }
    });
    // 理论上我只关注最终的状态，这里 debounce 3s，大概率能拿到最终状态
    watchDebounced(currentEffectLevel, (newValue, oldValue) => {
        sendEvent({
            name: 'EffectDowngradeLevelResult',
            result_type: String(newValue),
            extra_info: JSON.stringify({
                newLevel: newValue,
                isLowDevice: isLowDevice,
                temperatureGrade: temperatureGrade.value,
                baseEffectLevel: effectLevel.value,
            })
        });
    }, { debounce: 3000 });

    /** 对应动效降级等级是否显示 */
    const effectShowStatus = computed(() => ({
        L0: true,
        L1: !isLowDevice && currentEffectLevel.value < LevelEnum.L1,
        L2: !isLowDevice && currentEffectLevel.value < LevelEnum.L2,
        L3: !isLowDevice && currentEffectLevel.value < LevelEnum.L3,
    }));

    /** 视频降级 */
    const isVideoDowngrade = computed(() => isLowDevice || !!videoLevel.value);
    /** 音频降级等级 */
    const isAudioDowngrade = computed(() => audioLevel.value);
    /** 透明视频降级 */
    const isTransparentVideoDowngrade = computed(() => isLowBattery ? 0 : transparentVideoLevel.value);

    const switchEffectLevel = (state: LevelType) => {
        effectLevel.value = state;
    };

    const initOriginState = (state: {
        cny2025VideoLevel: LevelType;
        cny2025AudioLevel: LevelType;
        cny2025AnimationLevel: LevelType;
        cny2025TransparentVideoLevel: LevelType;
    }) => {
        videoLevel.value = state.cny2025VideoLevel;
        audioLevel.value = state.cny2025AudioLevel;
        effectLevel.value = state.cny2025AnimationLevel;
        transparentVideoLevel.value = state.cny2025TransparentVideoLevel;
        log('set level', state, isLowDevice);
    };

    return {
        /** 切换动效降级等级 */
        switchEffectLevel,
        /** 初始化降级状态 */
        initOriginState,
        /** 动效降级 */
        effectShowStatus,
        /** 视频降级 */
        isVideoDowngrade,
        /** 音频降级 */
        isAudioDowngrade,
        /** 透明视频降级 */
        isTransparentVideoDowngrade,
    };
});
