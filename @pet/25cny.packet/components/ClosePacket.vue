<script setup lang="ts">
import Popup from '@pet/adapt.popup/index.vue';
import Header from './Header/index.vue';
import Btn from '@pet/adapt.button/index.vue';
import type { TitleContext, Button, PrizeDetail, BottomInfo } from './type';
import { mainComponentsMap } from './config';
import Avatar from '@pet/adapt.avatar/index.vue';
import { computed, onMounted } from 'vue-demi';
import { isAtFourTab, isAtSearchTab } from '@pet/yau.core';
import type { AudioItem } from '@yoda/audio';
import { injectViewInfo } from '@pet/core.mobile/screenDetect';
import { transViewValue } from '@pet/core.mobile';

export interface ClosePacketProps {
    show?: boolean;
    sponsorLogo?: string;
    sponsorText?: string;
    title: string;
    titleContext?: TitleContext;
    subTitle?: string;
    subTitleIcon?: string;
    subTitleIconId?: number;
    blessingTitle?: string;
    blessing?: string;
    mainButton?: Button;
    subButton?: Button;
    bottomInfo?: BottomInfo;
    prizeDetail?: PrizeDetail;
    iconType?: 'lock' | 'open';
    styleType?: 'red' | 'gold';
    flyToTarget?: string;
    innerScroll?: boolean;
    playPopupShowSound?: AudioItem;
}

const props = withDefaults(defineProps<ClosePacketProps>(), {
    iconType: 'lock',
    styleType: 'red',
    innerScroll: false,
});

const emits = defineEmits<{
    (event: 'close'): void;
    (event: 'after-leave'): void;
    (event: 'main-click'): void;
    (event: 'mid-click'): void;
}>();

const onMidClick = () => {
    emits('mid-click');
};

const skinClass = computed(() => {
    return `close-packet-skin-${props.styleType}`;
});

const isAt4Tab = computed(() => isAtFourTab());
const isSearchTab = computed(() => isAtSearchTab());
const hasTab = computed(() => {
    return isAt4Tab.value || isSearchTab.value;
});
const { availableViewHeight } = injectViewInfo()!;
const scaleStyle = computed(() => {
    // 两套规则因为有tab的场景改了红包的居中方式，从包体居中改为整体居中
    if (availableViewHeight.value < transViewValue(hasTab.value ? 670 : 780)) {
        return hasTab.value ? 'scale-content-8' : 'scale-content-9';
    } else if (availableViewHeight.value < transViewValue(hasTab.value ? 700 : 799)) {
        return hasTab.value ? 'scale-content-95' : 'scale-content-96';
    }
    return '';
});

onMounted(() => {
    setTimeout(() => {
        props.playPopupShowSound?.play?.();
    }, 100);
});
</script>
<template>
    <Popup
        class="popup"
        :show="show"
        :fly-to-target="flyToTarget"
        :class="[skinClass, scaleStyle, hasTab ? 'lower-close' : 'normal-close']"
        ani-type="pop-packet-25cny"
        :enter-duration="867"
        light-type="packet"
        @afterLeave="emits('after-leave')"
        @close="emits('close')"
    >
        <template #addons>
            <Header
                :title="title"
                :assist-info="titleContext"
                :logo="sponsorLogo"
                :brand-logo-degrade-text="sponsorText"
            />
        </template>
        <div class="packet">
            <div class="content">
                <div class="title-content">
                    <div v-if="subTitle" class="sub-title u-fw-500">
                        <Avatar
                            v-if="subTitleIcon"
                            :author-id="subTitleIconId"
                            :src="subTitleIcon"
                            class="avatar"
                            :width="24"
                        /><span class="text">{{ subTitle }}</span>
                    </div>
                    <div v-if="blessingTitle" class="blessing-title u-fw-500">{{ blessingTitle }}</div>
                    <div v-if="blessing" class="blessing">{{ blessing }}</div>
                </div>
                <slot></slot>
                <template v-if="prizeDetail?.length">
                    <template v-for="(item, index) in prizeDetail">
                        <component
                            :is="mainComponentsMap[item.prizeType]"
                            v-if="item && mainComponentsMap[item.prizeType]"
                            :key="index"
                            v-bind="item"
                        ></component>
                    </template>
                </template>
            </div>
            <div :class="['mid-icon', iconType === 'lock' ? 'lock-icon' : 'open-icon']" @click="onMidClick"></div>
        </div>
        <template #footer>
            <Btn v-if="mainButton" type="primary-linear" class="btm-btn" @click="emits('main-click')">
                {{ mainButton.linkText }}
            </Btn>
            <slot name="footer"></slot>
        </template>
    </Popup>
</template>
<style>
:root {
    --cny-base-close-packet-main-bg-img: unset;
    --cny-base-close-packet-amount-color: unset;
    --cny-base-close-packet-amount-desc-color: unset;
    --cny-base-close-packet-amount-tag-bg-color: unset;
    --cny-base-close-packet-sub-title: unset;
    --cny-base-close-packet-sub-title-desc: unset;
    --cny-base-close-packet-sub-title-avatar-border-color: unset;
    --cny-base-close-packet-mid-lock-icon: unset;
    --cny-base-close-packet-mid-open-icon: unset;
    --cny-base-close-packet-btm-btn-bg-img: unset;
    --cny-base-close-packet-btm-btn-font-color: unset;
}
</style>

<style lang="scss" scoped>
.normal-close {
    --adapt-popup-close-btn-top-y-value: 26px;
}

.lower-close {
    --adapt-popup-close-btn-top-y-value: 36px;
}

.scale-content-8 {
    :deep(.transform-wrapper) {
        transform: scale(0.8);
    }
}

.scale-content-9 {
    :deep(.transform-wrapper) {
        transform: scale(0.9);
    }
}

.scale-content-95 {
    :deep(.transform-wrapper) {
        transform: scale(0.95);
    }
}

.scale-content-96 {
    :deep(.transform-wrapper) {
        transform: scale(0.96);
    }
}

.packet {
    position: relative;
    height: 402px;
    width: 300px;
    margin-top: 24px;
    background: no-repeat center/100%;
    background-image: var(--cny-base-close-packet-main-bg-img);

    .content {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 34px;
        --biz-base-packet-amount-color: var(--cny-base-close-packet-amount-color);
        --biz-base-packet-amount-tag-bg-color: var(--cny-base-close-packet-amount-tag-bg-color);
        --biz-base-packet-amount-desc-color: var(--cny-base-close-packet-amount-desc-color);

        .title-content {
            text-align: center;
            margin-bottom: 20px;

            .sub-title {
                width: 270px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20px;
                line-height: 26px;
                color: var(--cny-base-close-packet-sub-title);

                .avatar {
                    margin-right: 4px;
                    --adapt-avatar-border-width: 1px;
                    --adapt-avatar-border-color: var(--cny-base-close-packet-sub-title-avatar-border-color);
                }

                .text {
                    max-width: 240px;
                    text-align: center;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }

            .blessing-title {
                width: 160px;
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                margin: auto;
                margin-top: 50px;
                font-size: 22px;
                color: var(--cny-base-close-packet-sub-title);
            }

            .blessing {
                width: 220px;
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                margin: auto;
                margin-top: 16px;
                font-size: 15px;
                color: var(--cny-base-close-packet-sub-title);
            }
        }
    }

    .mid-icon {
        position: absolute;
        width: 146px;
        height: 146px;
        bottom: 75px;
        left: 77px;
        background: no-repeat center/100%;
    }

    .lock-icon {
        background-image: var(--cny-base-close-packet-mid-lock-icon);
    }

    .open-icon {
        background-image: var(--cny-base-close-packet-mid-open-icon);
    }
}

.btm-btn {
    margin-top: 23px;
    --adapt-button-width: 230px;
    --adapt-button-height: 72px;
    --adapt-button-primary-background-image: var(--cny-base-close-packet-btm-btn-bg-img);
    --adapt-button-primary-background-color: transparent;
    --adapt-button-primary-font-linear: var(--cny-base-close-packet-btm-btn-font-color);
    --adapt-button-font-size: 22px;
    font-family: KuaiYuanHuiTi, sans-serif;
}

.close-packet-skin-red {
    --cny-base-close-packet-main-bg-img: url('../assets/close-packet-bg.png');
    --cny-base-close-packet-amount-color: #fef6ca;
    --cny-base-close-packet-amount-desc-color: #fef6ca;
    --cny-base-close-packet-amount-tag-bg-color: rgba(255, 11, 36, 0.8);
    --cny-base-close-packet-sub-title: #fff4ca;
    --cny-base-close-packet-sub-title-desc: #fef6ca;
    --cny-base-close-packet-sub-title-avatar-border-color: #ffecc5;
    --cny-base-close-packet-mid-lock-icon: url('../assets/close-packet-icon-lock.png');
    --cny-base-close-packet-mid-open-icon: url('../assets/close-packet-icon-open.png');
    --cny-base-close-packet-btm-btn-bg-img: url('../assets/close-packet-btm-btn.png');
    --cny-base-close-packet-btm-btn-font-color: #ff0b24;
}

.close-packet-skin-gold {
    --cny-base-close-packet-main-bg-img: url('../assets/golden-close-packet-bg.png');
    --cny-base-close-packet-amount-color: #ba3608;
    --cny-base-close-packet-amount-desc-color: #ba3608;
    --cny-base-close-packet-amount-tag-bg-color: rgba(255, 255, 255, 0.5);
    --cny-base-close-packet-sub-title: #ba3608;
    --cny-base-close-packet-sub-title-desc: #ba3608;
    --cny-base-close-packet-sub-title-avatar-border-color: #ba3608;
    --cny-base-close-packet-mid-lock-icon: url('../assets/golden-close-packet-icon-lock.png');
    --cny-base-close-packet-mid-open-icon: url('../assets/golden-close-packet-icon-open.png');
    --cny-base-close-packet-btm-btn-bg-img: url('../assets/golden-close-packet-btm-btn.png');
    --cny-base-close-packet-btm-btn-font-color: #fff5e6;
}
</style>
