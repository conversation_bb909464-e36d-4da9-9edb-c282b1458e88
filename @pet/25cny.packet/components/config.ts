import Amount from './Amount/index.vue';
import Picture from './Picture/index.vue';
import MainPicture from './MainPicture/index.vue';
import AD_Picture from './AD_Picture/index.vue';
import LuckPicture from './LuckPicture/index.vue';
import Coupon from './Coupon/index.vue';
import Video from './Video/index.vue';

// 新增展示模块
export const mainComponentsMap = {
    amount: Amount,
    picture: Picture, // 道具图 102 * 102
    adPicture: AD_Picture, // 广告图 228 * 130
    mainPicture: MainPicture, // 主视觉图 220 * 150
    luckPicture: LuckPicture, // 福卡 100 * 110
    coupon: Coupon,  // 优惠券
    video: Video, // 视频
};

export enum ComponentType {
    Amount = 'amount',
    Picture = 'picture',
    AD_Picture = 'adPicture',
    MainPicture = 'mainPicture',
    LuckPicture = 'luckPicture',
    Coupon = 'coupon',
    Video = 'video',
}
