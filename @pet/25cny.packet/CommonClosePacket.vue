<!-- 关红包组件封装示例 -->
<script setup lang="ts">
import { ref } from 'vue-demi';
import ClosePacket from './components/ClosePacket.vue';
import type { ClosePacketProps } from './components/ClosePacket.vue';
import type { ClickPosition, PopupBtnClick } from './type';

const props = defineProps<
    ClosePacketProps & {
        btnClick?: PopupBtnClick;
    }
>();

// 处理业务逻辑

const emits = defineEmits<{
    (event: 'close'): void;
    (event: 'end', val: { event: string; data: ClosePacketProps }): void;
}>();

const position = ref<ClickPosition>('close');

const triggerBtnClick = () => {
    if (props.btnClick) {
        props.btnClick({
            position: position.value,
            destroy: () => {
                emits('close');
            },
        });
    } else {
        emits('close');
    }
};

const onCloseClick = () => {
    position.value = 'close';
    triggerBtnClick();
};

const onMainClick = () => {
    position.value = 'mainClick';
    triggerBtnClick();
};

const onMidClick = () => {
    position.value = 'mainClick';
    triggerBtnClick();
};

const onAfterLeave = () => {
    emits('end', {
        event: position.value,
        data: props,
    });
};
</script>

<template>
    <ClosePacket
        v-bind="props"
        @after-leave="onAfterLeave"
        @close="onCloseClick"
        @main-click="onMainClick"
        @mid-click="onMidClick"
    >
    </ClosePacket>
</template>

<style lang="scss" scoped></style>
