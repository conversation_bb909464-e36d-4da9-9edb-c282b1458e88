<script lang="ts">
export default {
    name: 'AdaptAvatarGroup',
};
</script>

<script lang="ts" setup>
import { computed } from 'vue-demi';
import Avatar from './index.vue';
import { px2rem } from '@pet/core.mobile';

export type AvatarGroupProps = {
    /**
     * 头像链接数组列表
     */
    srcs: string[];
    /**
     * 头像大小
     */
    width?: number;
    /**
     * 兜底头像
     */
    fallbackHead?: string;
    /**
     * 头像逆序
     */
    reverse?: boolean;
    /**
     * 最多展示个数
     */
    max?: number;
    /**
     * 多个头像蒙层形式 'none' | 'mask' | 'number' | 'icon'
     */
    restType?: 'none' | 'mask' | 'number' | 'icon';
    /**
     * 间距的值
     */
    gap?: number;
    /**
     * 兜底样式类型 “light” ｜ “dark” | "none"
     */
    defaultType?: 'none' | 'dark' | 'light';
};

const props = withDefaults(defineProps<AvatarGroupProps>(), {
    width: 38,
    reverse: false,
    more: false,
    max: 3,
    gap: 20,
    restType: 'none',
    defaultType: 'none',
});

const emit = defineEmits<{
    (e: 'click'): void;
}>();

const maxSrcs = computed(() => {
    return props.srcs.slice(0, props.max);
});

const getAvatar = (index: number) => {
    return {
        marginLeft: index === 0 ? '0' : `-${px2rem(props.width - props.gap)}`,
        zIndex: props.reverse ? props.srcs.length - index : 0,
    };
};

const firstAvatar = (index: number) => {
    return (props.reverse && index === 0) || (!props.reverse && index === maxSrcs.value.length - 1);
};

const getAvatarStatus = (index: number) => {
    return firstAvatar(index)
        ? props.restType === 'icon'
            ? 'more'
            : ['mask', 'number'].includes(props.restType)
              ? 'mask'
              : 'none'
        : 'none';
};
</script>

<template>
    <div class="avatar-group">
        <Avatar
            v-for="(src, index) in maxSrcs"
            :key="index"
            :width="width"
            class="avatar-item"
            :src="src"
            :style="getAvatar(index)"
            :default-type="defaultType"
            :status="getAvatarStatus(index)"
            :fallback-head="props.fallbackHead"
        >
            <!-- reverse slot在第一个头像的位置否则在最后一个 -->
            <div v-if="firstAvatar(index)" class="wrapper">
                <span v-if="restType === 'number' && srcs.length > max" class="rest-number"
                    >{{ srcs.length - max > 99 ? 99 : srcs.length - max }}+</span
                >
                <slot />
            </div>
        </Avatar>
    </div>
</template>

<style>
:root {
    /* 多人展示文字色 */
    --adapt-avatar-group-rest-number-color: #fff;
    /* 多人展示文字大小 */
    --adapt-avatar-group-rest-number-font-size: 18px;
}
</style>

<style lang="scss" scoped>
.avatar-group {
    position: relative;
    display: flex;
    align-items: center;
    font-size: 16px;

    .avatar-item {
        .wrapper {
            width: 100%;
            height: 100%;

            .rest-number {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                font-size: var(--adapt-avatar-group-rest-number-font-size);
                color: var(--adapt-avatar-group-rest-number-color);
            }
        }
    }
}
</style>
