import { resolve } from 'path';
import dotenv from 'dotenv';

dotenv.config();
const __pathname = new URL(import.meta.url).pathname;

function normalizeHref(href) {
    try {
        // 处理 //example.com 这种协议相对路径
        if (href.startsWith('//')) {
            // eslint-disable-next-line no-param-reassign
            href = `https:${href}`;
        }

        // 如果是完整的 URL，解析它
        if (href.includes('://') || href.startsWith('//')) {
            const url = new URL(href);
            // 只返回路径部分（不含协议和域名）
            return url.pathname;
        }

        // 如果是相对路径，直接返回
        return href;
    } catch (error) {
        console.warn(`警告: 无法解析链接 ${href}`);
        return href;
    }
}

const processCSS = (html) => {
    try {
        /**处理link css标签 */
        // 匹配所有的 CSS link 标签
        const cssLinkRegex = /<link[^>]*rel="stylesheet"[^>]*>/g;
        const cssLinks = html.match(cssLinkRegex) || [];

        // 去重处理
        const uniqueLinksMap = new Map();
        cssLinks.forEach((link) => {
            const hrefMatch = link.match(/href="([^"]*)"/);
            if (hrefMatch) {
                const href = hrefMatch[1];
                if (!href) {
                    return;
                }
                // 标准化 href
                const normalizedPath = normalizeHref(href);
                // 如果这个路径还没有对应的 link，则添加
                if (!uniqueLinksMap.has(normalizedPath)) {
                    uniqueLinksMap.set(normalizedPath, link);
                }
            }
        });

        const uniqueLinks = Array.from(uniqueLinksMap.values());

        // 从原HTML中移除所有的 CSS link 标签
        const htmlWithoutCss = html.replace(cssLinkRegex, '');

        // 将link的rel 由stylesheet换为preload
        const preloadCssLinkd = uniqueLinks.map((link) =>
            link.replace('stylesheet', 'preload').replace('>', ' as="style">'),
        );

        // 在 head 标签后插入去重后的 CSS link
        let processedHtml = htmlWithoutCss.replace(/<head>/, (match) => `${match}\n${preloadCssLinkd.join('\n')}`);

        // 将rel=stylesheet的css标签插入到head后面
        processedHtml = processedHtml.replace(/<\/head>/, (match) => `${uniqueLinks.join('\n')}${match}\n`);

        /**处理meta标签 */
        // 匹配所有的 meta 标签
        const metaRegex = /<meta[^>]*>/g;
        const metaTags = processedHtml.match(metaRegex) || [];

        // 从 HTML 中移除所有 meta 标签
        processedHtml = processedHtml.replace(metaRegex, '');

        // 在 head 标签后插入所有 meta 标签
        processedHtml = processedHtml.replace(/<head>/, (match) => `${match}\n${metaTags.join('\n')}`);
        return processedHtml;
    } catch (error) {
        console.error(`❌ ssg后处理时出错:`, error);
    }
};

const PreloadAssets = {
    // 每一项格式为 { href: string, as: string }
    '/home': [
        {
            href: '//p4-plat.wskwai.com/kos/nlav111449/activity/assets/xile.jpg-CsnI1xGo.avif',
            as: 'image',
            type: 'image/avif',
        },
        {
            href: '//p4-plat.wskwai.com/kos/nlav111449/activity/assets/jinghua.jpg-DS4cCQ2r.avif',
            as: 'image',
            type: 'image/avif',
        },
        {
            href: '//p4-plat.wskwai.com/kos/nlav111449/activity/assets/furao.jpg-DG8W3aBO.avif',
            as: 'image',
            type: 'image/avif',
        },
        {
            href: '//p4-plat.wskwai.com/kos/nlav111449/activity/assets/fengshou.jpg-CDtU2CLA.avif',
            as: 'image',
            type: 'image/avif',
        },
        {
            href: '//p4-plat.wskwai.com/kos/nlav111449/activity/assets/button-main-bg.png-Dyup1w31.avif',
            as: 'image',
            type: 'image',
        },
        {
            href: '//p4-plat.wskwai.com/kos/nlav111449/activity/assets/duocai.jpg-k94zQWjZ.avif',
            as: 'image',
            type: 'image/avif',
        },
        {
            href: '//p4-plat.wskwai.com/kos/nlav111449/activity/assets/jiangnan.jpg-u6KiykTK.avif',
            as: 'image',
            type: 'image/avif',
        },
        {
            href: '//p4-plat.wskwai.com/kos/nlav111449/activity/assets/yangfan.jpg-CWMkUSn2.avif',
            as: 'image',
            type: 'image/avif',
        },
    ],
};

const processPreload = (route, html) => {
    try {
        const assets = PreloadAssets[route];
        if (!assets) {
            return html;
        }

        const preloadLinks = assets.map(
            ({ href, as }) => `<link fetchPriority="high" rel="preload" href="${href}" as="${as}">`,
        );

        const processedHtml = html.replace(/<head>/, (match) => `${match}\n${preloadLinks.join('\n')}`);
        return processedHtml;
    } catch (error) {
        console.error('❌ 处理预加载标签时出错:', error);
    }
};

const processMetaTags = (html) => {
    try {
        // 匹配所有的 meta 标签
        const metaRegex = /<meta[^>]*>/g;
        const metaTags = html.match(metaRegex) || [];
        // 从 HTML 中移除所有 meta 标签
        let processedHtml = html.replace(metaRegex, '');
        // 重新插入所有 meta 标签
        processedHtml = processedHtml.replace(/<head>/, (match) => `${match}\n${metaTags.join('\n')}`);
        return processedHtml;
    } catch (error) {
        console.error('❌ 处理 meta 标签时出错:', error);
    }
};

export default {
    cdnConfig:
        process.env.NODE_ENV === 'dev'
            ? {
                  cdnBase: '/',
              }
            : {
                  // kcdn前缀
                  cdnBase: '/kos/nlav111449/activity/',
                  // 占位符域名
                  targetHostName: 'p4-plat.wskwai.com',
                  // kcdn下的所有域名
                  mathRule: ['p4-plat.wskwai.com', 'p5-plat.wskwai.com', 'p2-plat.wskwai.com', 'p66-plat.wskwai.com'],
              },
    spaPort: 5173,
    DOMContentLoadedScript: '', // 支持插入js脚本字符串，在DOMContentLoaded后执行,可选
    routeConfig: {
        // 和router里的base配置保持一致即可，这里是告诉无头浏览器要访问的path和下面routes一起定位到要渲染的页面
        routeBase: '/',
        // 核心是path。 query通常用不上，除非query能控制模块显示与影藏，layoutType为3很重要，这会影响ssg渲染的状态
        routes: ['home'].map((i) => ({
            path: i,
            query: { layoutType: 4 },
        })),
    },
    // ssg静态资源path，在构建页面时，内部会启动express服务，需要加载对应js资源，这里就是express 的static中间件参数
    staticPath: resolve(__pathname, '../dist'),
    enable: true, // 是否开启
    onPageRendered: (route, html) => {
        html = processPreload(route, html);
        html = processCSS(html);
        html = processMetaTags(html);

        return html;
    },
    enableScreenShot: false,
};
