<script setup lang="ts">
import Button from '@pet/adapt.button/index.vue';
import { ref, computed } from 'vue';

import { type SignInSelectProductView } from '@/services/open-api-docs/home/<USER>/schemas';

import ProductNullSvg from './assets/ProductNull.Svg.vue';

const props = withDefaults(
    defineProps<{
        /**
         * 为undefined代表是空商品
         */
        product?: SignInSelectProductView;

        /**
         * 布局方式
         * @default vertical
         */
        layout?: 'vertical' | 'horizontal';
        /**
         * 品个数
         */
        len: number;
        /**
         * 是否是默认选中
         */
        isDefault?: boolean;
    }>(),
    {
        layout: 'vertical',
    },
);

const emits = defineEmits<{
    (e: 'pick', buttonName: string): void;
    (e: 'select'): void;
}>();

const img = computed(() => props.product?.productIcon ?? '');
const title = computed(() => props.product?.productName ?? '');
const subtitle = computed(() => `价值${props.product?.originLLpe ?? 0}元`);
//const tagText = computed(() => `${(props.product?.allSignDays as string) ?? ''}天`);
//const selectNumberDesc = computed(() => props.product?.selectNumber ?? '');
const localLabel = computed(() => props.product?.localLabels?.[0] ?? '');
// 目前仅支持一个
const productLabel = computed(() => localLabel.value || '');
const remainCountText = computed(() => props.product?.selectNumText);
const selectedNumText = computed(() => props.product?.selectedNumText);

const isDisabled = computed(() => props.product?.productRemainStatus === 1 && !props.isDefault);
const btnText = computed(() => (props.isDefault ? '当前已选' : isDisabled.value ? '拿光啦' : '白拿这个'));
const isButtonBreathing = ref(false);

// TODO是否展示角标
const showBadge = ref(false);
const handlePick = (btnText: string) => {
    emits('select');
    setTimeout(() => {
        emits('pick', btnText);
    }, 100);
};
</script>

<template>
    <main class="product-item" :class="[`${layout}`]" @click="emits('select')">
        <template v-if="product">
            <img
                :id="isDefault ? 'default-product-img' : ''"
                :src="img"
                :alt="img"
                loading="lazy"
                @click="handlePick(btnText)"
            />
            <div v-show="showBadge" :class="['badge-outer']">
                <div :class="['badge-inner']">
                    <!-- <img class="product-item-badge" src="../imgs/badge.png" /> -->
                </div>
            </div>
            <div class="info-zone" :class="`info-zone--${layout}`">
                <div class="text-zone">
                    <div class="title">
                        {{ title }}
                    </div>
                    <div class="subtitle">
                        {{ subtitle
                        }}<span v-if="selectedNumText"> <span class="separate">|</span>{{ selectedNumText }}</span
                        ><span v-if="productLabel.length" class="choose-num-desc">{{ productLabel }}</span>
                    </div>
                </div>
                <Button
                    class="btn"
                    :breathing="isButtonBreathing"
                    :looks-like-disabled="isDisabled"
                    @click="handlePick(btnText)"
                >
                    <div class="btn__text">{{ btnText }}</div>
                    <div v-if="remainCountText" class="btn__sub">{{ remainCountText }}</div>
                </Button>
            </div>

            <div v-if="isDefault" class="tag">当前豪礼</div>
        </template>
        <template v-else>
            <div class="null-zone">
                <ProductNullSvg />
                <div class="null-zone__text">敬请期待更多好礼</div>
            </div>
        </template>
    </main>
</template>
<style lang="scss" scoped>
.vertical {
    flex-direction: column;
}
.horizontal {
    flex-direction: row;
}
.product-item {
    // 这里都是大图的样式
    --product-item-padding-top: 20px;
    --product-item-img-width: 100px;
    --product-item-img-height: 100px;
    --product-item-title-font-size: 14px;
    --product-item-title-line-height: 20px;
    --product-item-title-margin-top: 0px;
    --product-item-subtitle-font-size: 13px;
    --product-item-subtitle-margin-top: 7px;
    --product-item-btn-width: 146px;
    --product-item-btn-height: 50px;
    --product-item-btn-padding: 0 30px;
    --product-item-btn-margin-top: 13px;
    --product-item-btn-price-font-size: 16px;
    --product-item-btn-price-before-font-size: 15px;
    --product-item-btn-text-font-size: 16px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    padding-top: var(--product-item-padding-top);
    border-radius: 22px;
    background: #fff;
    overflow: visible;
    border: 2px solid transparent;

    .null-zone {
        margin-top: calc(88px - var(--product-item-padding-top));
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        &__text {
            margin-top: 10px;
            color: #c7c7c7;
            font-family: 'PingFang SC';
            font-size: 13px;
            font-style: normal;
            font-weight: normal;
            line-height: 14px /* 107.692% */;
        }
    }

    .info-zone {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        margin-top: 12px;
        width: 100%;
        height: fit-content;
        background-color: rgba(255, 255, 255, 1);
        padding-bottom: 15px;
        border-bottom-left-radius: 22px;
        border-bottom-right-radius: 22px;
    }
    .info-zone--horizontal {
        margin-left: 12px;
    }

    img {
        width: var(--product-item-img-width);
        height: var(--product-item-img-height);
    }
    .text-zone {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        margin-left: 12px;
        .title {
            color: #222;
            font-family: 'PingFang SC';
            font-size: var(--product-item-title-font-size);
            font-style: normal;
            font-weight: bold;
            line-height: var(--product-item-title-line-height);
            margin-top: var(--product-item-title-margin-top);
            max-width: 167px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .subtitle {
            color: #959595;
            font-family: 'PingFang SC';
            font-size: var(--product-item-subtitle-font-size);
            font-style: normal;
            font-weight: normal;
            line-height: 14px;
            margin-top: var(--product-item-subtitle-margin-top);
            max-width: 167px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            .separate {
                margin: 0 8px;
                font-size: 10px;
                color: rgba(0, 0, 0, 0.1);
            }
        }
        .choose-num-desc {
            height: 14px;
            border-radius: 4px;
            border: 0.5px solid rgba(254, 54, 102, 0.6);
            color: #fe3666;
            padding: 1px 4px;
            font-size: 10px;
            top: -1px;
            margin-left: 8px;
        }
    }

    .btn {
        --adapt-button-width: 100px;
        margin-top: var(--product-item-btn-margin-top);
        width: var(--product-item-btn-width);
        height: var(--product-item-btn-height);
        flex-shrink: 0;
        align-self: center;

        :deep(.btn-main-box) {
            padding: 0;
        }
        :deep(.btn-words) {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: var(--product-item-btn-padding);
        }

        &__price {
            color: #fe3666;
            font-family: MiSans;
            font-size: var(--product-item-btn-price-font-size);
            font-style: normal;
            font-weight: bold;
            line-height: 22px;
        }
        &__price::before {
            content: '¥';
            color: #fe3666;
            font-family: 'PingFang SC';
            font-size: var(--product-item-btn-price-before-font-size);
            font-style: normal;
            font-weight: bold;
            line-height: 14px;
            margin-right: 2px;
        }
        &__text {
            color: #fff;
            font-family: MiSans;
            font-size: var(--product-item-btn-text-font-size);
            font-style: normal;
            font-weight: bold;
            line-height: 22px;
        }
        &__sub {
            margin-top: 2px;
            color: #fff;
            text-align: center;
            font-family: 'PingFang SC';
            font-size: 10px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            opacity: 0.6;
        }
    }

    .tag {
        display: flex;
        align-items: center;
        justify-content: center;
        width: auto;
        height: 16px;
        position: absolute;
        top: -2px;
        left: -2px;
        padding: 4px 9px;
        border-top-left-radius: 12px;
        border-bottom-right-radius: 12px;
        background: linear-gradient(100deg, #ff7001 -1.65%, #fc2d39 47.88%, #f31906 98.68%);
        color: #fff;
        text-align: center;
        font-family: 'PingFang SC';
        font-size: 10px;
        font-style: normal;
        font-weight: bold;
        line-height: 16px;
    }

    &-badge {
        width: 59px !important;
        height: 47px !important;
    }

    .badge-outer {
        position: absolute;
        top: 10px;
        right: 22px;
        width: 59px;
        height: 47px;
    }

    .badge-inner {
        width: 59px;
        height: 47px;
    }
}
</style>
