<script lang="ts" setup>
import { toast } from '@pet/adapt.toast';
import ToggleSwitch from '@pet/adapt.toggle-switch/index.vue';
import { isAndroid, isIOS } from '@pet/yau.core';
import useCaptureDebugLog from '@pet/yau.logger';
import { invoke } from '@yoda/bridge';
import { ref, watch, computed } from 'vue';

import { useHomeApiModel } from '@/models/api/homeApiModel';
import { type CalendarEvent, useCalendarModel } from '@/models/calendarModel';
import { useTaskModel } from '@/models/taskModel';
import {
    checkCalendarAuth,
    changeEventForCalendar,
    deleteCalendarEvent,
    addCalendarEvent,
    CalendarMethod,
    CalendarRes,
} from '@/utils/calendar';
const { log } = useCaptureDebugLog('calendar');
const { toastText, saveCalendarEventId, deleteCalendarEventId } = useCalendarModel();
const { refreshHome } = useHomeApiModel();
const { tasksRefetch } = useTaskModel();

const props = withDefaults(
    defineProps<{
        calendarEvent: CalendarEvent;
        openToast?: string;
        closeToast?: string;
        openToastUseServerText?: boolean;
        closeNeedConfirm?: boolean;
    }>(),
    {
        openToast: '已开启打卡提醒',
        closeToast: '已关闭打卡提醒',
        openToastUseServerText: false,
        closeNeedConfirm: false,
    },
);

const emits = defineEmits<{
    (e: 'toggle', checked: boolean): void;
}>();

// const { useVisibilityChange } = injectVisibility()!;
const delimiter = '#';

const isChecked = ref(false);
const isLoading = ref(false);

const localEventIds = ref('');

const calendarEvents = computed(() => props.calendarEvent.calendarEventList!);

const calendarEventIds = computed(
    () => localEventIds.value || (Boolean(props.calendarEvent?.eventId) ? `${props.calendarEvent?.eventId}` : '') || '',
); // 需要兼容calendarEvent.eventId为null的情况
const calendarEventIdList = computed(() => calendarEventIds.value.split(delimiter).filter((id) => id) ?? []);

// 删除日历提醒, 先删除第一天, 如果没有删除成功, 则检查是否授权, 如果没有授权，则弹出授权toast
// 否则都尝试继续删除其他日历
const deleteCalendar = async () => {
    const eventId = calendarEventIdList.value[0];
    console.log('delete calendar event id', eventId);
    const { success } = await deleteCalendarEvent(eventId);
    if (!success && !(await checkCalendarAuth())) {
        return {
            deleteSucc: false,
            auth: false,
        };
    }

    return Promise.all(
        calendarEventIdList.value.slice(1).map((eventId) => {
            return deleteCalendarEvent(eventId);
        }),
    ).then(async () => {
        // bridge有删除失败的情况
        // 当使用服务端记录的日历事件判断日历开启状态时，跨设备登录同一账号，由于新设备有服务端记录的eventid但设备上没有日历事件，会导致删除bridge失败。应跳过此次报错，当做已成功删除处理。
        const { result } = await deleteCalendarEventId({
            eventId: calendarEventIds.value,
        });
        const deleteSucc = result === 1;

        if (deleteSucc) {
            localEventIds.value = '';
            refreshHome('LUCK_SHAKE_SUDOKU');
        }

        return {
            deleteSucc,
            auth: true,
        };
    });
};

const addOneCalendar = async () => {
    const event = calendarEvents.value[0];
    // android下，如果是多天事件，需要减去一天，否则会多一天
    const endDay =
        typeof event?.endDay === 'number' && isAndroid() && event?.endDay !== event?.startDay
            ? event?.endDay - 1000 * 60 * 60 * 24
            : event?.endDay;

    const res = await addCalendarEvent({
        bizKey: 'ft_growth',
        scene: 'sum_op_25',
        periodType: 'everyday',
        title: event?.title ?? '',
        note: event?.note ?? '',
        url: event?.url ?? '',
        startTime: event?.startDay ?? 0,
        endTime: endDay ?? 0,
    });
    console.log('add calendar one res', res, res.success && res?.eventId, endDay);
    if (res.success && res?.eventId) {
        return res.eventId;
    }
};

// 添加日历提醒
// eslint-disable-next-line sonarjs/cognitive-complexity
const addCalendar = async (options?: { needRefreshHome?: boolean; needRefreshTask?: boolean }) => {
    // 默认刷新主接口
    const needRefreshHome = options?.needRefreshHome ?? true;
    const needRefreshTask = options?.needRefreshTask ?? true;
    // 临时写法，等客户端能力支持后优化：获取用户权限失败，则不触发后续写入事件
    const firstEventId = await addOneCalendar();
    if (!firstEventId) {
        console.log('add calendar fail---firstEventId');
        return false;
    }
    return Promise.all(
        calendarEvents.value.slice(1).map((event: any) => {
            return changeEventForCalendar({
                method: CalendarMethod.ADD,
                event: {
                    type: 1,
                    ...event,
                    endDay:
                        typeof event?.endDay === 'number' && isAndroid() && event?.endDay !== event?.startDay
                            ? event?.endDay - 1000 * 60 * 60 * 24
                            : event?.endDay,
                },
            });
        }),
    ).then(async (bridgeResults) => {
        const [succBridgeResults, failBridgeResults] = bridgeResults.reduce(
            ([succs, fails], bridgeResult) => {
                console.log('bridgeResult', bridgeResult);
                if (Boolean(bridgeResult)) {
                    const { result, eventId } = bridgeResult;
                    console.log('result', result, !eventId, result !== CalendarRes.SUCCESS);
                    const fail = result !== CalendarRes.SUCCESS || !eventId;
                    if (fail) {
                        console.log('fail', result, eventId);
                        fails.push(bridgeResult);
                    } else {
                        succs.push(bridgeResult);
                    }
                    return [succs, fails];
                }
                return [succs, fails];
            },
            [[] as typeof bridgeResults, [] as typeof bridgeResults],
        );

        if (failBridgeResults.length) {
            // bridge有添加失败的情况，将添加成功的删除
            [firstEventId, ...succBridgeResults.map((item: any) => item.eventId)].forEach((eventId) => {
                changeEventForCalendar({
                    method: CalendarMethod.DELETE,
                    event: {
                        type: 1,
                        eventId,
                    },
                });
            });
            return false;
        }
        const eventIds = [firstEventId, ...bridgeResults.map(({ eventId }) => eventId)].join(delimiter);
        const { result } = await saveCalendarEventId({
            eventId: eventIds,
        });
        if (result === 1) {
            if (needRefreshHome) {
                refreshHome('LUCK_SHAKE_SUDOKU');
            }
            if (needRefreshTask) {
                // 因为服务端更新了添加日历的状态，这里要刷新一下任务列表更新下添加日历任务
                tasksRefetch();
            }
            localEventIds.value = eventIds;
        } else {
            // 服务端有存储失败的情况（比如获取did为空），将添加成功的删除
            [firstEventId, ...succBridgeResults.map((item: any) => item.eventId)].forEach((eventId) => {
                changeEventForCalendar({
                    method: CalendarMethod.DELETE,
                    event: {
                        type: 1,
                        eventId,
                    },
                });
            });
        }
        return result === 1;
    });
};

const showAddCalendarToast = (addSucc: boolean, isAuth?: boolean) => {
    const openToast = props.openToastUseServerText ? toastText.value : props.openToast;
    console.log('openToast', openToast, isAuth, isIOS() && !isAuth);
    const failedToast =
        isIOS() && !isAuth ? '开启打卡提醒失败，请去【设置】内开启日历权限后再开启' : '开启打卡提醒失败，请稍后再试';
    toast(addSucc ? openToast : failedToast);
};

// 添加日历提醒(避免重复)
const checkAndAddCalendar = async (options: {
    needRefreshHome: boolean;
    needRefreshTask?: boolean;
    checkIOSAuth?: boolean;
}) => {
    if (!isChecked.value) {
        const isAuth = await checkCalendarAuth();
        const checkIOSAuth = options.checkIOSAuth ?? true;

        let addSucc = false;
        if (checkIOSAuth) {
            // iOS下，如果没有授权，则不触发后续写入事件
            addSucc = isIOS() ? isAuth && (await addCalendar(options)) : await addCalendar(options);
        } else {
            addSucc = await addCalendar(options);
        }

        showAddCalendarToast(addSucc, isAuth);
        return addSucc;
    } else {
        // 兜底逻辑, 直接弹已开启 toast
        showAddCalendarToast(true, true);
        return true;
    }
};

const handleDeleteCalendar = async () => {
    const noAuthToast = '关闭提醒失败，请去【设置】内开启日历权限后再关闭';
    const { deleteSucc, auth } = await deleteCalendar();
    isChecked.value = !deleteSucc;
    toast(auth ? (deleteSucc ? props.closeToast : '关闭提醒失败') : noAuthToast);
    emits('toggle', !deleteSucc);
};

const changeRemindSwitch = async (checked: boolean) => {
    isChecked.value = checked;

    if (isLoading.value) {
        isChecked.value = !checked;
        return false;
    }

    isLoading.value = true;
    if (checked) {
        const isAuth = await checkCalendarAuth();
        // iOS下，如果没有授权，则不触发后续写入事件
        // const addSucc = isIOS() ? isAuth && (await addCalendar()) : await addCalendar();
        const addSucc = await addCalendar();
        showAddCalendarToast(addSucc, isAuth);
        log('changeRemindSwitch--addSucc', addSucc);
        isChecked.value = addSucc;
        emits('toggle', addSucc);
        isLoading.value = false;
        return;
    }

    if (props.closeNeedConfirm) {
        const { target } = await invoke('ui.showDialog', {
            content: '关闭提醒后可能错过打卡，是否确定要关闭？',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
        });
        if (target === 'confirm') {
            await handleDeleteCalendar();
        } else {
            isChecked.value = !checked;
            log('changeRemindSwitch--close', isChecked.value);
        }
    } else {
        await handleDeleteCalendar();
    }

    isLoading.value = false;
};

const init = ref(false);
async function setCalendar() {
    // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions
    isChecked.value = !!props.calendarEvent.eventId;

    // 防止开关状态闪动，在初次有效search后，更新init标记
    if (props.calendarEvent.calendarEventList !== undefined && !init.value) {
        init.value = true;
    }
}

watch(
    () => props.calendarEvent?.eventId,
    (val) => {
        // 更新本地数据（需要兼容val为null的情况）
        localEventIds.value = Boolean(val) ? `${val}` : '';
        setCalendar();
    },
    { immediate: true },
);
// useVisibilityChange((visible) => {
//     setCalendar();
// });

defineExpose({
    init,
    checked: isChecked,
    calendarEventIdList,
    addCalendar,
    checkAndAddCalendar,
    deleteCalendar,
    addCalendarAndToast: () => changeRemindSwitch(true),
});
</script>

<template>
    <div>
        <slot :checked="isChecked"></slot>
        <ToggleSwitch :show="isChecked" @toggle="changeRemindSwitch"></ToggleSwitch>
    </div>
</template>
