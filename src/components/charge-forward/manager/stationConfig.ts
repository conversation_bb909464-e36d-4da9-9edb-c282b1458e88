const BaoZaConfig = {
    loader: () => import('../assets/effects/baoZa/EffectIndex.vue'),
    limitDay: [1, 2], // 天津金币包子只在第一第二天展示
};
const XianConfig = {
    loader: () => import('../assets/effects/xian/EffectIndex.vue'),
};
const SofiaConfig = {
    loader: () => import('../assets/effects/sofia/EffectIndex.vue'),
};
const QingdaoConfig = {
    loader: () => import('../assets/effects/qingdao/EffectIndex.vue'),
};

const FoodBgEffect = defineAsyncComponent({
    loader: () => import('../assets/effects/foodBg/EffectIndex.vue'),
    delay: 150,
});
const BuildingBgEffect = defineAsyncComponent({
    loader: () => import('../assets/effects/buildingBg/EffectIndex.vue'),
    delay: 150,
});

// 站点的key
export enum StationKeyEnum {
    // 1-10
    'tianjin' = 'tianjin',
    'beijing' = 'beijing',
    'shijiazhuang' = 'shijiazhuang',
    'baoding' = 'baoding',
    'handan' = 'handan',
    'tangshan' = 'tangshan',
    'cangzhou' = 'cangzhou',
    'langfang' = 'langfang',
    'zhangjiakou' = 'zhangjiakou',
    'qinhuangdao' = 'qinhuangdao',
    // 11-20
    'changchun' = 'changchun',
    'yanbian' = 'yanbian',
    'jian' = 'jian',
    'dalian' = 'dalian',
    'shenyang' = 'shenyang',
    'anshan' = 'anshan',
    'tieling' = 'tieling',
    'mohe' = 'mohe',
    'qiqihaer' = 'qiqihaer',
    'haerbin' = 'haerbin',
    // 21-30
    'xian' = 'xian',
    'huayin' = 'huayin',
    'yanan' = 'yanan',
    'taiyuan' = 'taiyuan',
    'datong' = 'datong',
    'jincheng' = 'jincheng',
    'huhehaote' = 'huhehaote',
    'eerduosi' = 'eerduosi',
    'wulanchabu' = 'wulanchabu',
    'baotou' = 'baotou',
    // 31-40
    'jinan' = 'jinan',
    'qingdao' = 'qingdao',
    'heze' = 'heze',
    'weifang' = 'weifang',
    'zibo' = 'zibo',
    'zhengzhou' = 'zhengzhou',
    'xuchang' = 'xuchang',
    'kaifeng' = 'kaifeng',
    'zhumadian' = 'zhumadian',
    'luoyang' = 'luoyang',
    // 41-50
    'suzhou' = 'suzhou',
    'xuzhou' = 'xuzhou',
    'nanjing' = 'nanjing',
    'shanghai' = 'shanghai',
    'hefei' = 'hefei',
    'hangzhou' = 'hangzhou',
    'quzhou' = 'quzhou',
    'jinyun' = 'jinyun',
    'yiwu' = 'yiwu',
    'jinhua' = 'jinhua',
    // 51-60
    'quanzhou' = 'quanzhou',
    'fuzhou' = 'fuzhou',
    'xiamen' = 'xiamen',
    'putian' = 'putian',
    'shaxian' = 'shaxian',
    'fuding' = 'fuding',
    'zhangzhou' = 'zhangzhou',
    'nanping' = 'nanping',
    'taibei' = 'taibei',
    'nantouxian' = 'nantouxian',
}

export enum StationTypeEnum {
    Food = 'food',
    Building = 'building',
}
export const StationBgEffect = {
    [StationTypeEnum.Food]: FoodBgEffect,
    [StationTypeEnum.Building]: BuildingBgEffect,
};

// 站点key -- 类型：食物
export const StationHasFoodType = [
    StationKeyEnum.tianjin,
    StationKeyEnum.baoding,
    StationKeyEnum.tangshan,
    StationKeyEnum.qiqihaer,
    StationKeyEnum.jinhua,
    StationKeyEnum.shaxian,
];

// 站点key -- 动效
export const StationEffectEnum: {
    [key in StationKeyEnum]?: {
        loader: () => Promise<any>;
        limitDay?: number[];
    };
} = {
    [StationKeyEnum.tianjin]: BaoZaConfig,
    [StationKeyEnum.xian]: XianConfig,
    [StationKeyEnum.haerbin]: SofiaConfig,
    [StationKeyEnum.qingdao]: QingdaoConfig,
};
