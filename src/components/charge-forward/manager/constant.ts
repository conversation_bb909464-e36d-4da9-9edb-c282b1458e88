import { GridShape, type BuildingConfig, type BuildingType, type GridllwardConfig, type MapConfig } from './config';
// import FuRao from '../assets/bg/furao.jpg';
// import Jing<PERSON><PERSON> from '../assets/bg/jinghua.jpg';
// import XiLe from '../assets/bg/xile.jpg';

// demo测试模拟数据
// demo地图配置
export const JingHuaMapConfig: MapConfig = {
    imgs: {
        width: 414,
        height: 1799,
    },
    offset: -1799 / 2 - 8,
    maskHeight: 140 /** mask 完全不透明高度 */,
    gridShape: GridShape.Square,
};

export const XiLeMapConfig: MapConfig = {
    imgs: {
        width: 414,
        height: 1799,
    },
    offset: -1799 / 2 - 158 * 3 + 16,
    maskHeight: 140,
    gridShape: GridShape.Square,
};

export const FuRaoMapConfig: MapConfig = {
    imgs: {
        width: 414,
        height: 1799,
    },
    offset: -1799 / 2 + 20 - 30,
    maskHeight: 140,
    gridShape: GridShape.Square,
};

export const FengShouMapConfig: MapConfig = {
    imgs: {
        width: 414,
        height: 1799,
    },
    offset: -1799 / 2 - 8,
    maskHeight: 140,
    gridShape: GridShape.Square,
};
export const JiangNanMapConfig: MapConfig = {
    imgs: {
        width: 414,
        height: 1799,
    },
    offset: -1799 / 2 - 8,
    maskHeight: 140,
    gridShape: GridShape.Square,
};
export const YangFanMapConfig: MapConfig = {
    imgs: {
        width: 414,
        height: 1799,
    },
    offset: -1799 / 2 - 8,
    maskHeight: 140,
    gridShape: GridShape.Square,
};
export const DuoCaiMapConfig: MapConfig = {
    imgs: {
        width: 414,
        height: 1799,
    },
    offset: -1799 / 2 - 8 - 152,
    maskHeight: 140,
    gridShape: GridShape.Square,
};
export enum MapKeyType {
    JingHua = 'jinghua',
    XiLe = 'xile',
    FuRao = 'furao',
    FengShou = 'fengshou',
    Jiangnan = 'jiangnan',
    Yangfan = 'yangfan',
    Duocai = 'duocai',
    Default = 'default',
}

export const MapConfigData: Record<MapKeyType, MapConfig> = {
    [MapKeyType.JingHua]: JingHuaMapConfig,
    [MapKeyType.XiLe]: XiLeMapConfig,
    [MapKeyType.FuRao]: FuRaoMapConfig,
    [MapKeyType.FengShou]: FengShouMapConfig,
    [MapKeyType.Jiangnan]: JiangNanMapConfig,
    [MapKeyType.Yangfan]: YangFanMapConfig,
    [MapKeyType.Duocai]: DuoCaiMapConfig,
    [MapKeyType.Default]: JingHuaMapConfig,
};

export const RenderZIndex = {
    // station: 85,
    // IP: 90,
    // Llwd: 89,
    // Building: 87,
    // GridSkin: 80,
    CurrentGrid: 3, // 格子反馈
    GridSkin: 10, // lowest zIndex
    // Building: 20, // since building is sorted, this is not used
    IP: 50, //base zIndex
};

// demo格子奖励配置
// export const DemoGridllwardConfig: GridllwardConfig = {
//     demo: {
//         icon: '',
//         steps: [3, 4, 5, 10, 12, 14, 16, 17, 20],
//     },
// };

// 建筑格子奖励配置

// 每八个格子有三个空位，索引从0开始
// 假设每一组loop有9个空位,在一个loop里pos代表对应的建筑出现的空位索引
// export const DemoBuildingConfig: BuildingConfig = {
//     interval: 9,
//     data: {
//         demo2: {
//             type: 'decoration' as BuildingType,
//             iconUrl: 'https://p5-pro.kskwai.com/kcdn/cdn-kcdn112307/guestTree/she.png',
//             pos: [2, 3, 4],
//             width: 124,
//             height: 124,
//         },
//     },
// };
