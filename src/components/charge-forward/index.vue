<script setup lang="ts">
import { MapKeyType } from '@/components/charge-forward/manager/constant';
import { SoundType, useAudioModel } from '@/models/audioModel';
import { useConfigModel } from '@/models/configModel';
import { useForwardRushBtnModel } from '@/models/forwardRushBtnModel';
import { useForwardRushGridModel } from '@/models/forwardRushGridModel';
import { useHomeModel } from '@/models/homeModel';
import { useSnapShotModel } from '@/models/snapShotModel';
import { inHeadless, setLocalMapConfig } from '@/utils/ssg';

import BuildingItem from './building/BuildingItem.vue';
import GridReact from './gridReact/index.vue';
import LlwardGroup from './llward/LlwardGroup.vue';
import { ChargeForwardManager } from './manager/manager';
import GridSkin from './skin/GridSkin.vue';
import StationBuilding from './station/StationBuilding.vue';
import UserIp from './user-ip/index.vue';

const {
    manager,
    container,
    llwardGrids,
    buildings,
    currentGrid,
    stations,
    gridSkins,
    mapConfig,
    currentStep,
    stationConfig,
    backgroundStyle,
    gllwardGridLayout,
    buildingConfig,
    gridSkinConfig,
    mapKey,
    popoverContainerStyle,
} = useForwardRushGridModel();
const { isArrived } = useSnapShotModel();
const { signedStep, llwardAniController, todaySigned, isFirstDay } = useForwardRushBtnModel();
const { themeConfig } = useConfigModel();
const { playSound } = useAudioModel();
const canLoad = ref(false);
const { localPrefix, homeData } = useHomeModel();

onMounted(() => {
    setTimeout(() => {
        canLoad.value = true;
    }, 300);
});

watch(
    [container, currentStep, stationConfig, gllwardGridLayout, mapConfig, gridSkinConfig, isArrived],
    ([con, currentStep, station, llwards, mapConfig, gridSkinConfig, isArrived]) => {
        if (con) {
            const { width, height } = con.getBoundingClientRect();
            manager.value = new ChargeForwardManager({
                isArrived,
                currentStep,
                signedStep: signedStep.value,
                mapConfig,
                gridllwardConfig: llwards,
                buildingConfig: buildingConfig.value,
                stationConfig: station,
                gridSkinConfig,
                viewSize: {
                    width,
                    height,
                },
                mapKey: mapKey.value,
                localPrefix,
            });
            (window as any).manager = manager.value;
        }
    },
);

watch(
    [homeData, manager],
    ([home, manager]) => {
        if (home && manager) {
            manager.onMoveEnd(() => {
                playSound(SoundType.IP_DOWN);
            });
            setLocalMapConfig(`${localPrefix}forwardRushGridBackGroundMapKey`, `${mapKey.value ?? MapKeyType.Default}`);
        }
    },
    {
        deep: true,
        immediate: true,
    },
);

/**
 * 用户IP展示规则：
 * 第一天&&已签到&&弹框未打开
 * 第一天&&未签到
 * 非第一天&&未签到
 */
const showIp = computed(
    () =>
        (isFirstDay.value && todaySigned.value && !isArrived.value) ||
        (isFirstDay.value && !todaySigned.value) ||
        (!isFirstDay.value && !todaySigned.value),
);
</script>
<template>
    <div
        v-if="!inHeadless()"
        ref="container"
        class="charge-forward-container"
        :class="`theme-${mapKey ?? MapKeyType.Default}`"
        :style="{
            ...backgroundStyle,
            'background-color': themeConfig?.backgroundColor ?? '#ffffff',
        }"
    >
        <div id="game-popover" class="charge-forward-popover" :style="popoverContainerStyle"></div>
        <!-- 展示规则：当天打卡成功后不展示 -->
        <UserIp v-if="showIp" />
        <template v-for="station in stations">
            <StationBuilding
                v-if="station && canLoad"
                :id="station.uniqueKey!"
                :key="station.uniqueKey!"
                :station="station"
            />
        </template>
        <template v-for="llward in llwardGrids">
            <LlwardGroup v-if="llward && canLoad" :key="llward.type" :llward="llward" />
        </template>
        <template v-for="build in buildings">
            <BuildingItem v-if="build && build.name && canLoad" :key="build.name" :build="build" />
        </template>
        <template v-for="skin in gridSkins">
            <GridSkin
                v-if="skin"
                :key="skin.step"
                :name="gridSkinConfig?.gridSkinSponsor ?? ''"
                :url="gridSkinConfig?.gridSkinUrl ?? ''"
                :position="skin"
            />
        </template>
        <GridReact
            v-if="currentGrid"
            :position="currentGrid.position"
            :shape="currentGrid.shape"
            :step="currentGrid.step"
        ></GridReact>
        <Teleport to="#app" :disabled="!llwardAniController.show.value">
            <component
                :is="llwardAniController.comp.value"
                :show="llwardAniController.show.value"
                :name="llwardAniController.name.value"
                @ended="llwardAniController.endAni"
            />
        </Teleport>
    </div>
    <div v-else id="ssg-map" class="ssg-map"></div>
</template>
<style scoped lang="scss">
.charge-forward-container {
    width: 100%;
    height: 100%;
    background-size: 100% auto;
    background-repeat: repeat-y;
    overflow: hidden;
    z-index: 91;
}
.charge-forward-popover {
    // background-color: aqua;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 91;
}

.ssg-map {
    position: absolute;
    left: 0;
    top: 0;
    background-position: right 0 top var(--charge-forward-background-offset-size);
    background-color: var(--charge-forward-background-ssg-bg-color);
    width: 100%;
    height: 100%;
    background-size: 100% auto;
    background-repeat: repeat-y;
    overflow: hidden;
    z-index: 91;
}

.theme-default {
    @include bg('./assets/bg/jinghua.jpg');
}
.theme-jinghua {
    @include bg('./assets/bg/jinghua.jpg');
}

.theme-xile {
    @include bg('./assets/bg/xile.jpg');
}
.theme-furao {
    @include bg('./assets/bg/furao.jpg');
}
.theme-fengshou {
    @include bg('./assets/bg/fengshou.jpg');
}
.theme-duocai {
    @include bg('./assets/bg/duocai.jpg');
}
.theme-jiangnan {
    @include bg('./assets/bg/jiangnan.jpg');
}
.theme-yangfan {
    @include bg('./assets/bg/yangfan.jpg');
}
</style>
