<script setup lang="ts">
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import GuideHand from '@pet/adapt.guide/index.vue';
import NumberScroll from '@pet/adapt.number-scroll/index.vue';
import { debounce } from '@pet/yau.core';
import { whenever } from '@vueuse/core';

import CountDownNum from '@/components/sign-count-down-btn/CountDownNum.vue';
import { useLogger } from '@/init/logger';
import { useAnimationPause } from '@/models/animationVisibility';
import { ForwardRushBtnStatus, useConfigModel } from '@/models/configModel';
import { useForwardRushBtnModel } from '@/models/forwardRushBtnModel';
import { useSignInModel } from '@/models/signInModel';
import { BeginnerGuideStrategy } from '@/types/abTest';
import { inHeadless } from '@/utils/ssg';

const Effect8536 = defineAsyncComponent(() => import('@/@effect/effect_8536/EffectIndex.vue'));

import MainBtn, { type AniType } from './main-btn/MainBtn.vue';
import { HugeSignInStatus, PopupType } from '@/services/open-api-docs/home/<USER>/schemas';
import { useHomeModel } from '@/models/homeModel';

const {
    enableForwardRush,
    rushCount,
    mainButtonStatus,
    buttonAndBubbleInfo,
    handleForwardRushBtnClick,
    showBeginPopup,
    isNoStep,
    abTestGroup,
    isFirstDay,
    todaySigned,
    todayIndex,
} = useForwardRushBtnModel();
const { timerState } = useSignInModel();
const { effectShowStatus } = useDowngradeLevel();
const { isAnimationPause } = useAnimationPause();
const { homeData, signInStatus } = useHomeModel();

const BUTTON_TEXT_IN_PROCESSING = '前进中';

const { kconfConfig, weakGuideConfig } = useConfigModel();

// 隐藏touchend动画的条件: 今日未签到 && (剩余次数大于0 或者 有免签卡)
const hideTouchEndAni = computed(() => {
    console.log('rushCount hideTouchEndAni', rushCount.value);
    return (
        (mainButtonStatus.value === ForwardRushBtnStatus.UN_SIGN && rushCount.value > 0) ||
        mainButtonStatus.value === ForwardRushBtnStatus.SIGN_FREE
    );
});

// 是否在进行中
const inProcessing = ref(false);
const handleTouchStart = (e: TouchEvent) => {
    e.preventDefault();
    if (hideTouchEndAni.value && !inProcessing.value && enableForwardRush.value) {
        inProcessing.value = true;
        return;
    }
};
// 向前冲的过程中需要将按钮的文案变为“前进中”
const mainText = computed(() => {
    if (inProcessing.value) {
        return BUTTON_TEXT_IN_PROCESSING;
    }
    return buttonAndBubbleInfo.value.buttonText;
});

// 走格子流程结束之后需要重置一下processing的值
whenever(enableForwardRush, () => {
    inProcessing.value = false;
});

/**
 * 点击向前冲按钮
 */
const handleClickForwardRush = debounce(() => {
    handleForwardRushBtnClick();
});

const aniType = computed<AniType>(() => {
    if (showBeginPopup.value) {
        return 'begin-shine-breath';
    }
    if (mainButtonStatus.value === ForwardRushBtnStatus.SIGN_FREE) {
        return 'shine-breath';
    }
    return 'shine';
});

const { sendShow } = useLogger();
whenever(
    () => buttonAndBubbleInfo.value.buttonText,
    (val) => {
        sendShow('OP_ACTIVITY_JION_BUTTON', {
            title: val,
            steps: rushCount.value,
        });
    },
    { immediate: true },
);

/**
 * 小手展示时机：第一天且有新手引导时，不展示小手引导
 * And 当天未打卡
 * And 仅进行中的用户
 * And 打卡天数<=配置天数
 */
const showGuideMainButton = computed(() => {
    const isFirstDayAndHasGuide =
        isFirstDay.value &&
        homeData.value?.popList?.[0]?.popupType === PopupType.BEGINNER_GUIDE &&
        abTestGroup.value !== BeginnerGuideStrategy.SecondTest;
    const isLessThanConfigDay = todayIndex.value && todayIndex.value <= kconfConfig.value?.mainButtonGuideHandShowDays;
    return (
        !isFirstDayAndHasGuide &&
        !todaySigned.value &&
        signInStatus.value === HugeSignInStatus.PROCESSING &&
        isLessThanConfigDay
    );
});

// 小手引导消失动画的开始时间
const hideTime = computed(() => {
    // 实验1，小手消失动画延迟9.4s
    if (abTestGroup.value === BeginnerGuideStrategy.FirstTest) {
        return '9.4s';
        // 实验4，小手消失动画延迟10.2s
    } else if (abTestGroup.value === BeginnerGuideStrategy.FourthTest) {
        return '10.2s';
    } else {
        return '6.4s';
    }
});
</script>

<template>
    <div class="forward-btn-container">
        <!-- 主按钮主体 -->
        <MainBtn
            id="MAIN_BTN"
            v-click-log="{
                action: 'OP_ACTIVITY_JION_BUTTON',
                params: {
                    title: buttonAndBubbleInfo.buttonText,
                    steps: rushCount,
                },
            }"
            v-guide.target="{
                id: `GUIDE_MAIN_BUTTON`,
                priority: 10,
                enabled: showGuideMainButton,
                component: Effect8536,
                offset: {
                    top: -20,
                    right: -10,
                },
                // 消失时间
                duration: 10000,
                nonInterval: 3000,
                onTargetClick: () => {
                    handleClickForwardRush();
                    return true;
                },
                ...weakGuideConfig?.GUIDE_MAIN_BUTTON,
            }"
            class="forward-btn"
            :is-no-step="isNoStep"
            :hide-touch-end-ani="hideTouchEndAni"
            :in-processing="inProcessing"
            :disabled="!enableForwardRush"
            :ani-type="aniType"
            :pause="!enableForwardRush || (isAnimationPause && !showBeginPopup)"
            :effect-status="effectShowStatus.L1"
            :css-status="effectShowStatus.L1"
            :in-a-b-test="abTestGroup === BeginnerGuideStrategy.FirstTest"
            @click="handleClickForwardRush"
        >
            <div v-if="!inHeadless()" class="forward-btn-text real-button-area" @touchstart="handleTouchStart">
                <span class="forward-btn-text-main">{{ mainText }}</span>
                <div v-if="mainButtonStatus === ForwardRushBtnStatus.SIGN_INTERCEPTED">
                    <div class="forward-btn-count-down u-font-family">
                        <CountDownNum
                            v-if="timerState && timerState?.days !== 0"
                            :time="timerState.days"
                            :line-height="20"
                            :font-size="10"
                            suffix="天"
                        />
                        <CountDownNum
                            v-if="timerState"
                            :time="timerState.hours"
                            :line-height="20"
                            :font-size="10"
                            suffix="时"
                        />
                        <CountDownNum
                            v-if="timerState"
                            :time="timerState.minutes"
                            :line-height="20"
                            :font-size="10"
                            suffix="分"
                        />
                        <CountDownNum
                            v-if="timerState && timerState.days === 0"
                            :time="timerState.seconds"
                            :line-height="20"
                            :font-size="10"
                            suffix="秒"
                        />
                        <span class="count-down-desc u-font-family">{{ buttonAndBubbleInfo.buttonSubText }}</span>
                    </div>
                </div>
                <div
                    v-else-if="buttonAndBubbleInfo.buttonSubText && !isNoStep"
                    class="forward-btn-text-sub u-font-family"
                >
                    <span class="left-count-side u-fw-500 u-font-family">{{ buttonAndBubbleInfo.buttonSubText }}</span>
                    <NumberScroll
                        :font-size="13"
                        :line-height="18"
                        :number-value="Number(rushCount)"
                        :animation-duration="367"
                        :item-effect-delay="0"
                        :to-fixed="0"
                        :immediate-effect="false"
                    />
                </div>
            </div>
        </MainBtn>
        <Effect8536 v-if="effectShowStatus.L1 && showBeginPopup" :allow-hide="true" :hide-time="hideTime" />
        <!-- 解决按钮的点击热区问题 -->
        <div class="bottom-mask"></div>
    </div>
</template>

<style lang="scss" scoped>
.forward-btn-container {
    width: 214px;
    height: 128px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    padding-top: 17px;

    .bottom-mask {
        width: 214px;
        height: 35px;
        background-color: transparent;
        position: absolute;
        bottom: 0;
        z-index: 2;
    }

    :deep(.number-node) {
        --adapt-number-scroll-font-weight: 400;
    }

    .forward-btn {
        position: relative;
        --adapt-button-main-font-family: unset;

        .real-button-area {
            width: 206px;
            height: 76px;
            border-radius: 100px;
        }

        .disabled {
            opacity: 0.5;
        }

        &-count-down {
            display: flex;
            flex-direction: row;
            margin-top: 4px;
            height: 20px;
            color: #fff;

            :deep(.count-down-num) {
                background-color: rgba(255, 255, 255, 0.2);
                width: 20px;
                height: 20px;
                line-height: 20px;
                color: #ffffff;
                font-family: MiSans;
                border-radius: 6px;
            }

            :deep(.count-down-separator) {
                color: #ffffff;
                font-size: 10px;
                line-height: 20px;
                margin: 0 4px;
            }

            .count-down-desc {
                font-size: 10px;
                line-height: 20px;
                margin-left: -1px;
            }
        }

        &-text {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 2;

            &-main {
                text-align: center;
                width: 100%;
                font-family: KuaiYuanHuiTi;
                font-size: 26px;
                transform: skew(-7deg);
                font-weight: 400;
                height: 28px;
                line-height: 28px;
                letter-spacing: 1px;
                background: linear-gradient(346deg, #fff2c6 6.59%, #fff 67.44%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            &-sub {
                margin-top: 4px;
                height: 18px;
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
                color: #fff;
                font-size: 13px;
                line-height: 18px;

                .left-count-side {
                    margin-right: 3px;
                }
            }
        }
    }
}
</style>
