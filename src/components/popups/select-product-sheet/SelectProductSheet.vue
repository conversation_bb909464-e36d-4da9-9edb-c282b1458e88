<script setup lang="ts">
import type { PilotType } from '@pet/adapt.popup/types';
import { screenDetect } from '@pet/core.mobile/screenDetect';

import ClonePilot from '@/components/common-modals/ClonePilot.vue';
import CommonSheet from '@/components/common-sheet/CommonSheet.vue';
import ProductContainer from '@/components/product/ProductContainer.vue';
import { vShowLog } from '@/init/logger';
import { type SignInSelectProductView } from '@/services/open-api-docs/home/<USER>/schemas';
const props = defineProps<{
    title: string;
    subTitle: string;
    /* 弹窗顶部标题 */
    topTitle?: string;
    /* 副标题标签 */
    firstSelectTag?: string;
    productList: SignInSelectProductView[];
    finalProduct: SignInSelectProductView;
    handlePickItem: (
        v?: SignInSelectProductView,
        index?: string,
        btnName?: string,
        logSource?: string,
        close?: () => void,
    ) => void;
    setChoiceProductAnim: (show: boolean, afterUrl?: string) => void;
    logSource?: string;
    popupType?: string;
}>();

const emit = defineEmits<{
    (e: 'end', type: 'close' | 'pick'): void;
    (e: 'close'): void;
}>();
const close = () => {
    emit('end', 'pick');
};
const pilotRef = ref<PilotType | null>(null);
const show = ref(true);
const showPilot = ref(false);

const handleBtnClick = (v?: SignInSelectProductView, index?: string, btnName?: string) => {
    // 第一个商品为默认选品，有飞入动效
    if (index && index === '0') {
        showPilot.value = true;
        // 执行飞入动画
        pilotRef.value?.boot();
        props?.setChoiceProductAnim(true);
        // 关闭弹窗
        show.value = false;
        // 动画执行结束后触发end事件
        setTimeout(() => {
            props.handlePickItem(v, index, btnName, props?.logSource, close);
        }, 500);
    } else {
        props.handlePickItem(v, index, btnName, props?.logSource, close);
    }
};
const isSmallScreen = screenDetect().isSmallScreenDevice;
const scrollableRef = ref<HTMLDivElement>();
// 记录列表是否滚动
const isScrolled = ref(false);

// 处理滚动事件的函数
const handleScroll = () => {
    if (scrollableRef.value && scrollableRef.value.scrollTop > 0) {
        isScrolled.value = true;
    } else {
        isScrolled.value = false;
    }
};

onMounted(() => {
    if (scrollableRef.value) {
        // 添加滚动事件监听器
        scrollableRef.value.addEventListener('scroll', handleScroll);
    }
});

onUnmounted(() => {
    if (scrollableRef.value) {
        // 移除滚动事件监听器
        scrollableRef.value.removeEventListener('scroll', handleScroll);
    }
});
const flyTargetEnd = ref(false);
const onFlyPilotEnd = () => {
    flyTargetEnd.value = true;
    emit('end', 'close');
};
const onAfterLeave = () => {
    if (!showPilot.value) {
        emit('end', 'close');
    }
};
</script>
<template>
    <CommonSheet
        v-model:show="show"
        v-show-log="{
            action: 'OP_ACTIVITY_REWARD_CHOOSE_POP',
            params: {
                // @ts-expect-error
                source: logSource ?? '',
            },
        }"
        class="select-product-sheet"
        :class="{ 'small-screen': isSmallScreen }"
        :title="title"
        @after-leave="onAfterLeave"
    >
        <template #top
            ><div v-if="topTitle" class="top-title">{{ topTitle }}</div></template
        >
        <template #header>
            <div class="product-header">
                <div v-if="finalProduct" class="final-product">
                    <img v-if="finalProduct.productIcon" class="product" :src="finalProduct.productIcon" />
                    <div class="desc">当前豪礼</div>
                </div>
            </div>
        </template>
        <template #subTitle
            ><div>
                {{ subTitle
                }}<span v-if="firstSelectTag" class="sub-title-tag"
                    ><span class="text">{{ firstSelectTag }}</span></span
                >
            </div></template
        >
        <div ref="scrollableRef" class="product-container-wrapper" :class="{ mask: isScrolled }">
            <ProductContainer
                id="product-list-container"
                :tab-products="productList"
                @pick-item="handleBtnClick"
            ></ProductContainer>
        </div>
    </CommonSheet>
    <ClonePilot
        ref="pilotRef"
        fly-to-target="progress-view"
        fly-el="default-product-img"
        hide-source-el="popup-sheet"
        :duration="467"
        :bezier="[0, 0, -47, -84.5]"
        @end="onFlyPilotEnd"
    ></ClonePilot>
</template>
<style lang="scss" scoped>
.select-product-sheet {
    --adapt-sheet-max-show-height: 100%;
    --adapt-sheet-small-screen-max-show-height: 100%;
    --adapt-button-primary-background-image: linear-gradient(100deg, #ff7001 -1.65%, #fc2d39 47.88%, #f31906 98.68%);
    :deep(.sheet-top) {
        top: -66px;
    }
    :deep(.sheet-content) {
        min-height: unset;
    }
    :deep(.sheet-body) {
        overflow: hidden;
    }
    :deep(.header) {
        .sheet-sub-title {
            top: 108px;
        }
    }
    .top-title {
        display: block;
        text-align: center;
        font-family: KuaiYuanHuiTi;
        font-size: 30px;
        font-weight: 400;
        line-height: 34px /* 113.333% */;
        transform: skew(-7deg);
        background: linear-gradient(357deg, #ffeeb1 -11.05%, #fff 88.91%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    .product-header {
        width: 414px;
        .final-product {
            position: absolute;
            width: 84px;
            height: 94px;
            right: 24px;
            top: 38px;
            z-index: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .product {
                width: 78px;
                height: 78px;
            }
            .desc {
                margin-top: -13px;
                padding: 4px 8px;
                border-radius: 20px;
                background: rgba(8, 8, 8, 0.46);
                color: #fff;
                font-family: 'PingFang SC';
                font-size: 12px;
                font-style: normal;
                font-weight: 500;
                line-height: 16px;
            }
            .icon {
                width: 9px;
                height: 9px;
                margin-left: 2px;
            }
        }
    }
    .sub-title-tag {
        position: absolute;
        display: flex;
        right: -7px;
        height: 18px;
        padding: 0 4px;
        margin-top: 1px;
        transform: translate(100%, -100%);
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        border: 1px solid #ffd695;
        background: linear-gradient(98deg, #fce9be 7.97%, #ffead0 53.7%, #fdbc85 104.23%);
        text-align: center;
        font-size: 11px;

        .text {
            font-family: 'PingFang SC';
            font-size: 11px;
            font-style: normal;
            font-weight: 600;
            line-height: 12px /* 109.091% */;
            background: linear-gradient(94deg, #a14d03 0%, #c27812 49.43%, #7b3a00 99.47%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }
    .product-container-wrapper {
        margin-top: -3px;
        max-height: calc((553) * 100vh / 896);
        overflow-y: auto;
        &::-webkit-scrollbar {
            display: none;
        }
    }
    .mask {
        -webkit-mask-image: linear-gradient(
            180deg,
            rgba(255, 246, 244, 0) 0%,
            rgba(255, 246, 244, 0.5) 1.82%,
            #fff6f4 29.71%
        );
    }
    &.small-screen {
        --adapt-sheet-max-show-height: 100%;
        .product-container-wrapper {
            max-height: calc((460) * 100vh / 896);
        }
    }
}
</style>
