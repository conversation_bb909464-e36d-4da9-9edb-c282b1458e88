<script lang="ts" setup>
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import AdaptStroke from '@pet/adapt.stroke-text/index.vue';
import PictureImg from '@pet/quantum.PictureImg';

import TitleEffect from '@/@effect/effect_8343/EffectIndex.vue';
import { useConfigModel } from '@/models/configModel';
import { useForwardRushBtnModel } from '@/models/forwardRushBtnModel';
import { useHomeModel } from '@/models/homeModel';
import { BeginnerGuideStrategy } from '@/types/abTest';

import beggingImg from './@effect/effect_8253/assets/fallback.png?preset=modern';
import BeginnerEffect from './@effect/effect_8253/index.vue';

const { effectShowStatus } = useDowngradeLevel();
const { showBeginPopup } = useForwardRushBtnModel();
const { kconfConfig } = useConfigModel();
const { abTestConfigView } = useHomeModel();

const emits = defineEmits<{
    (event: 'end'): void;
}>();

const animation = ref('fade_in');
// 视频播放异常
const playerError = ref(false);

/** AB实验组 */
const abTestGroup = computed(() => abTestConfigView.value?.beginnerGuideStrategy ?? 0);

const hide = () => {
    animation.value = 'fade_out';
    setTimeout(() => {
        emits('end');
    }, 333);
};
const hidePongPong = () => {
    showBeginPopup.value = false;
};

// 显示怦怦跳
showBeginPopup.value = true;

const mainBtnDom = ref();
// 按下时 砰砰跳消失
const hideBtnAni = () => {
    hidePongPong();
    // 第一天阻塞接口，打卡流程走完后才可以刷新home
    // blockingRefreshHandler(true); // 向前冲会阻塞刷新
    mainBtnDom.value?.removeEventListener('touchstart', hideBtnAni);
};

// 抬起时 关闭引导，然后向前冲
const handleClick = () => {
    hide();
    mainBtnDom.value?.removeEventListener('touchend', handleClick);
};

onMounted(() => {
    mainBtnDom.value = document.querySelector('#app #MAIN_BTN');
    if (!mainBtnDom.value) {
        return;
    }
    // 提高层级
    (mainBtnDom.value as HTMLElement).style.zIndex = '101';
    mainBtnDom.value.addEventListener('touchstart', hideBtnAni);
    mainBtnDom.value.addEventListener('touchend', handleClick);
    let totalTime = 7200;
    if (abTestGroup.value === BeginnerGuideStrategy.FirstTest) {
        totalTime = 11500;
    } else if (abTestGroup.value === BeginnerGuideStrategy.FourthTest) {
        totalTime = 11000;
    }
    setTimeout(hideBtnAni, totalTime);
});

const handlePopHide = () => {
    let duration = 2200;
    if (abTestGroup.value === BeginnerGuideStrategy.FirstTest) {
        duration = 3500;
    } else if (abTestGroup.value === BeginnerGuideStrategy.FourthTest) {
        duration = 6000;
    }
    setTimeout(handleClick, duration);
};
</script>

<template>
    <Teleport to="#app .forward-btn-container">
        <div class="beginner-guide" :class="animation">
            <div class="image">
                <BeginnerEffect
                    v-if="effectShowStatus.L1 && !playerError"
                    :ab-test="abTestGroup"
                    @error="playerError = true"
                    @play="handlePopHide"
                />
                <PictureImg v-else :src="beggingImg" @load="handlePopHide" />
            </div>

            <!-- 标题 -->
            <div v-if="!!kconfConfig?.guideTitle" class="title">
                <TitleEffect v-if="effectShowStatus.L1" />
                <div class="text-effect">
                    <AdaptStroke :text="kconfConfig.guideTitle" />
                </div>
            </div>
        </div>
    </Teleport>
</template>

<style lang="scss" scoped>
.beginner-guide {
    position: fixed;
    z-index: 100;
    top: 0;
    width: 414px;
    height: 100%;
    background-color: #000d;

    .image {
        position: absolute;
        top: var(--status-bar-height);
        bottom: 40px;
        width: 100%;

        display: flex;
        justify-content: center;
        align-items: center;

        box-sizing: border-box;

        .PictureImg {
            height: 100%;
            width: 100%;

            :deep(img) {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }
    }

    .title {
        position: absolute;
        bottom: 210px;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;

        pointer-events: none;
        z-index: 102;

        .text-effect {
            position: absolute;
            top: 20px;
            --adapt-stroke-text-font-family: kuaiyuanhui;
            --adapt-stroke-text-font-size: 30px;
            --adapt-stroke-text-line-height: 46px;
            --adapt-stroke-text-color: linear-gradient(92.68deg, #ff6b00 7.48%, #ff0f00 83.91%);
            --adapt-stroke-text-stroke-width: 6px;
            opacity: 0;
            animation: fadeIn 333ms 200ms linear forwards;
        }
    }
}

.fade_in {
    animation: fadeIn 333ms forwards;
}
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/** 淡出 */
.fade_out {
    animation: fadeOut 333ms forwards;
    @keyframes fadeOut {
        0% {
            opacity: 1;
        }
        100% {
            opacity: 0;
        }
    }
}
</style>
