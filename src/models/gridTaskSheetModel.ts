import { createModel, useModel } from '@gundam/model';
import type { Task } from '@pet/adapt.queue-api/main/TaskQueue';
import { toast } from '@pet/adapt.toast';
import { formatResponseTaskInfo } from '@pet/work.task-list-core/utils/framework/DefaultImpl/ListDataModule/formatter';
import { TaskActionType, type ResponseTask } from '@pet/work.task-list-core/utils/framework/DefaultImpl/types';
import useCaptureDebugLog from '@pet/yau.logger';

// import { useLogger } from '@/init/logger';
import { shareModel } from '@/models/shareModel';

// import { useAudioModel } from './audioModel';
import type { CommonTaskDetail } from '@/services/open-api-docs/home/<USER>/schemas';

import { forwardRushApiModel } from './api/forwardRushApiModel';
import { homeModel } from './homeModel';
import { CUSTOM_POPUP_TYPE, QUEUE_TAGS_TYPE, TaskType, usePopupModel } from './popup.model';
import { switchModel } from './switchModel';
import { ExecuteType, useTaskExecuteModel } from './taskExecuteModel';

export const gridTaskSheetModel = createModel(({ getModelInstance }) => {
    const { openPopup } = usePopupModel();

    const { log } = useCaptureDebugLog('gridTaskSheetModel');
    const { executeTask } = useTaskExecuteModel();
    const { moveProgress } = useModel(forwardRushApiModel);
    const { homeProgress } = useModel(homeModel);
    const { hasShowedGridTaskSheet } = useModel(switchModel);

    const currentGridTask = ref<CommonTaskDetail>();

    watch(
        [() => moveProgress?.value, () => homeProgress?.value],
        () => {
            const moveTs = moveProgress?.value?.currentTime ?? 0;
            const homeTs = homeProgress?.value?.currentTime ?? 0;

            currentGridTask.value =
                moveTs > homeTs
                    ? (moveProgress?.value?.currentGridTaskDetail?.commonTaskDetail ?? currentGridTask.value)
                    : (homeProgress?.value?.currentGridTaskDetail?.commonTaskDetail ?? currentGridTask.value);
        },
        { immediate: true },
    );

    // 格式化后的格子任务
    const formattedCurrentGridTask = computed(() => {
        if (currentGridTask.value) {
            return formatResponseTaskInfo(currentGridTask.value as ResponseTask);
        }
        return undefined;
    });

    // const { playSound } = useAudioModel();

    /** 任务点击处理 */
    const handleGridTaskBtnClick = async (close: () => void) => {
        const task = currentGridTask.value;
        const formattedTask = formattedCurrentGridTask.value;

        if (!task || !formattedTask) {
            toast('数据异常，请刷新页面重试');
            return false;
        }

        const actionType = formattedTask.action.type;
        // 双端互拉任务
        if (actionType === TaskActionType.PullEach) {
            executeTask(task as ResponseTask, ExecuteType.pullEach, '', 0, false);
        }
        // 观看直播任务
        if (actionType === TaskActionType.JsBridge) {
            executeTask(task as ResponseTask, ExecuteType.JSBridge, '', 0, false);
        }

        // 激励视频任务
        if (actionType === TaskActionType.AdIncentiveVideo) {
            executeTask(task as ResponseTask, ExecuteType.encourageVideo, '', 0, false);
        }

        // 暑期观看直播任务调用快链
        if (actionType === TaskActionType.NativeVisit) {
            executeTask(task as ResponseTask, ExecuteType.jumpToNative, '', 0, false);
        }

        // push权限任务
        if (actionType === TaskActionType.OpenNotification) {
            executeTask(task as ResponseTask, ExecuteType.reservePush, '', 0, false);
        }

        // 分享任务
        if (actionType === TaskActionType.Share) {
            getModelInstance(shareModel)?.share({
                subBiz: task.shareSubBiz ?? '',
                taskToken: task.taskToken,
                logExt: {
                    taskId: task.taskId,
                },
            });
        }
    };

    const sheetTask = ref<Task | null>();

    // 打开挑战格子_任务半屏
    const openGridTaskSheet = (priority: number) => {
        log(`openGridTaskSheet_调用_${priority}`);
        if (sheetTask.value) {
            hasShowedGridTaskSheet.value = true;
            return false;
        }

        sheetTask.value = openPopup({
            component: () => import('@/components/popups/grid-task-sheet/GridTaskSheet.vue'),
            data: {
                popupType: CUSTOM_POPUP_TYPE.GRID_TASK_SHEET,
            },
            options: {
                name: TaskType.SPECIAL_SHEET,
                queueTags: [QUEUE_TAGS_TYPE.SHEET, QUEUE_TAGS_TYPE.POPUP],
                priority,
            },
        });

        hasShowedGridTaskSheet.value = true;

        sheetTask.value?.end.finally(() => {
            sheetTask.value = null;
        });
    };

    // 关闭挑战格子_任务半屏
    const closeGridTaskSheet = async () => {
        const task = sheetTask.value;
        if (!task) {
            return false;
        }
        task.triggerDestroy();
        await task.end;
    };

    return {
        currentGridTask,
        openGridTaskSheet,
        closeGridTaskSheet,
        handleGridTaskBtnClick,
        sheetTask,
    };
});

export const useGridTaskSheetModel = () => useModel(gridTaskSheetModel);
