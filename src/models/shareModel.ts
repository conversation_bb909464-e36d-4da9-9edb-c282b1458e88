import { createModel, useModel } from '@gundam/model';
import * as _ from '@pet/quantum.share';
import type { TaskActionShare } from '@pet/work.task-list-core/utils/framework/DefaultImpl/types';
import type { TaskInfo } from '@pet/work.task-list-core/utils/types';

import { useTaskSheetState } from '@/hooks/useTaskSheetState';
import { reportKeyActionEnd, reportKeyActionStart } from '@/utils/log/keyActionLog';
import type { SummerPopup, TASK_SOURCE } from '@/utils/popupTransform/types';

import { useShareApiModel } from './api/shareApiModel';
import { useConfigModel } from './configModel';
import { QUEUE_TAGS_TYPE, TaskType, usePopupModel } from './popup.model';
import { safeParseJson } from './utils/safeJsonParse';

export const SubBiz = {
    /** 主页面分享 */
    PAGESHARE: 'SN_SU_MAIN_PAGESHARE',
    /** 每日打卡成功 */
    SIGN: 'SN_SU_MAIN_SIGN',
    /** 完成最终打卡，获得奖品 */
    SIGN_FINISH: 'SN_SU_MAIN_SIGN_FINISH',
    /** 补签分享 */
    BUQIAN_SHARE: 'SN_SU_MAIN_BUQIAN_SHARE',
    /** 邀请组队 */
    ZUDU_SHARE: 'SN_SU_MAIN_ZUDU_SHARE',
    /** 喊TA */
    ZUDU_ASK: 'SN_SU_MAIN_ZUDU_ASK',
    /** 组队TASK任务 */
    ZUDU_TASK: 'SN_SU_MAIN_JUMP',
    /** 向前冲下发奖品炫耀 */
    // JUMP: 'SN_SU_MAIN_JUMP',
    /** 分享任务得次数 */
    SHARE_TASK: 'SN_SU_MAIN_SHARE_TASK',
    /** 限时拉人任务-拉满X个人（x个首活、x个新）给1笔 */
    XS: 'SN_SU_MAIN_XS',
    /** 限时拉人任务-拉满X个回给1笔 */
    XSLH: 'SN_SU_MAIN_XSLH',
    /** 拉人翻倍任务 */
    // FANBEI: 'SN_SU_MAIN_FANBEI',
    /** 拉活得次数 */
    // CISHU: 'SN_SU_MAIN_CISHU',
    /** 常驻拉人任务 */
    LR: 'SN_SU_MAIN_LR',
    /** 三合一拉人任务 */
    LR_SANHEYI: 'SN_SU_MAIN_LR_SANHEYI',
    /** 钱包提现分享 */
    QBTX_SHARE: 'SN_SU_QBTX_SHARE',

    /** Pad扫码 */
    PAD_MAIN_TASK: 'SN_SU_PAD_MAIN_TASK',
} as const;

// 分享参数的类型，分享方法的类型 + 任务 token
type ShareParams<T extends (...args: any) => any> = Parameters<T>[0] & { taskToken?: string };

/**
 * 分享 Model
 * 对分享方法二次封装
 * 1. 从后端获取 shareCommonParam 等参数
 * 2. 任务分享时，携带 taskToken
 *
 * @see https://docs.corp.kuaishou.com/d/home/<USER>
 */
export const shareModel = createModel(() => {
    const { fetchShareParams, fetchShareParamsLoading } = useShareApiModel();
    const { kconfUEConfig, sharePoster } = useConfigModel();
    const { openSummerPopup } = usePopupModel();
    const { isCurrentTaskSheetShow } = useTaskSheetState();
    // const { shareConfig, shareABSubbizList } = useConfigModel();

    const handleParams = async <T extends ShareParams<typeof _.share>>(params: T) => {
        const { taskToken, ...p } = params;
        const placeholder: Record<string, string> = { ...params.placeholder };

        /** 加入携带参数 */
        // 1. 分享海报参数
        // placeholder.sharePoster = sharePoster.value; // 分享海报，todo-lsq 看tk是否需要海报根据主题变化
        // https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/share/sign_finish.png
        // 2. 活动参数
        const shareCommonParam = (await fetchShareParams(params.subBiz)).shareCommonParam;
        shareCommonParam && (placeholder.shareCommonParam = shareCommonParam); // 回流参数
        // 3. 任务 token
        taskToken && (placeholder.taskToken = taskToken); // 任务 token

        return { ...p, placeholder };
    };

    /** 分享主方法，点进 _.share 有详细的参数定义 */
    const share = async (params: ShareParams<typeof _.share>) => {
        if (fetchShareParamsLoading.value) {
            return;
        }
        // 4. 加个雷达上报面板拉起成功率
        reportKeyActionStart({ name: `share_common` });
        return _.share({
            ...(await handleParams(params)),
            onPanelShow: () => {
                reportKeyActionEnd({ name: `share_common` });
            },
        });
    };

    /** 指定渠道分享，直接拉起微信QQ，不拉起分享面板 */
    const directShare = async (params: ShareParams<typeof _.directShare>) => _.directShare(await handleParams(params));

    /** 依次尝试拉起 微信、QQ、口令 */
    const shareChain = async (params: ShareParams<typeof _.shareChain>) => _.shareChain(await handleParams(params));

    const openSharePopup = (taskInfo: TaskInfo, source?: TASK_SOURCE) => {
        const { action, taskToken, extParams } = taskInfo;
        const subBiz = (action as TaskActionShare)?.subBiz ?? '';

        if ('popupView' in extParams) {
            const popupView = safeParseJson<SummerPopup>(extParams.popupView);
            openSummerPopup(
                popupView!,
                {
                    taskType: TaskType.ACTIVE_POPUP,
                    config: {
                        queueTags: isCurrentTaskSheetShow.value
                            ? [QUEUE_TAGS_TYPE.TASK_SHEET_POPUP]
                            : [QUEUE_TAGS_TYPE.POPUP],
                    },
                    extraClickHandler: (_, buttonInfo, close, position) => {
                        if (position === 'mainClick' || position === 'bottomClick') {
                            share({ subBiz, taskToken });
                        }
                        close();
                        return true;
                    },
                },
                source,
            );
        }
    };

    /** TK: 每日打卡成功，分享打卡天数，传入天数0-99 */
    const shareSign = (days: number) => {
        if (days < 0 || days > 99) {
            console.error('分享天数不合法，范围0-99');
            return;
        }
        const numberImgs = [
            kconfUEConfig.value.shareNumber0,
            kconfUEConfig.value.shareNumber1,
            kconfUEConfig.value.shareNumber2,
            kconfUEConfig.value.shareNumber3,
            kconfUEConfig.value.shareNumber4,
            kconfUEConfig.value.shareNumber5,
            kconfUEConfig.value.shareNumber6,
            kconfUEConfig.value.shareNumber7,
            kconfUEConfig.value.shareNumber8,
            kconfUEConfig.value.shareNumber9,
        ];

        return share({
            subBiz: SubBiz.SIGN,
            placeholder: {
                dayOneImage: numberImgs[Math.floor(days / 10)], // 十位
                dayTwoImage: numberImgs[days % 10], // 个位
            },
        });
    };

    /** TK: 完成最终打卡，获得奖品 */
    const shareSignFinish = (finalRewardImage: string, finalRewardName: string) => {
        return share({ subBiz: SubBiz.SIGN_FINISH, placeholder: { finalRewardImage, finalRewardName } });
    };

    return {
        /** 分享主方法 */
        share,
        /** 指定渠道分享 */
        directShare,
        /** 依次尝试拉起 微信、QQ、口令 */
        shareChain,
        openSharePopup,
        /** 整个页面的分享 */
        shareHome: () => share({ subBiz: SubBiz.PAGESHARE }),
        /** TK: 每日打卡成功 */
        shareSign,
        /** TK: 完成最终打卡，获得奖品 */
        shareSignFinish,
        /** 补签分享 */
        shareBuQian: () => share({ subBiz: SubBiz.BUQIAN_SHARE }),
        /** 邀请组队 */
        shareZuDu: () => share({ subBiz: SubBiz.ZUDU_SHARE, placeholder: { bizDomain: window.location.host } }),
        /** 提醒队友打开分享 */
        shareZuDuAsk: () => share({ subBiz: SubBiz.ZUDU_ASK, type: 'normal' }),
        /** 向前冲下发奖品炫耀 */
        // shareJump: () => share({ subBiz: SubBiz.JUMP }),
        /** 分享任务得次数 */
        shareTask: () => shareChain({ subBiz: SubBiz.SHARE_TASK }),
        /** 限时拉人任务-拉满X个人（x个首活、x个新）给1笔 */
        shareXS: () => shareChain({ subBiz: SubBiz.XS }),
        /** 限时拉人任务-拉满X个回给1笔 */
        shareXSLH: () => shareChain({ subBiz: SubBiz.XSLH }),
        /** 拉人翻倍任务，一期删掉 */
        // shareFanBei: () => shareChain({ subBiz: SubBiz.FANBEI }),
        /** 拉活得次数，，一期删掉 */
        // shareCishu: () => shareChain({ subBiz: SubBiz.CISHU }),
        /** 常驻拉人任务 */
        shareLR: () => shareChain({ subBiz: SubBiz.LR }),
        /** 三合一拉人任务 */
        shareLRSanHeYi: () => shareChain({ subBiz: SubBiz.LR_SANHEYI }),
        /** 钱包提现分享 */
        shareQBTX: () => share({ subBiz: SubBiz.QBTX_SHARE }),
    };
});

export const useShareModel = () => useModel(shareModel);
