import { createUseModel } from '@gundam/model';
import { useGuideState } from '@pet/25cny.guide-directive';
import { useTaskQueueModel } from '@pet/adapt.task-queue/index';
import { toast } from '@pet/adapt.toast';
import useCaptureDebugLog from '@pet/yau.logger';

import { DefaultReplaceProductConfirmText } from '@/common/const/signIn';
import { TOAST_LIMIT_PRODUCT, SELECT_PRODUCT_SUCCESS, TOAST_DEFAULT_PRODUCT } from '@/common/const/toast';
// import CommonModal from '@/components/common-modals/CommonModal.vue';
import { useLogger } from '@/init/logger';
import { useConfigModel } from '@/models/configModel';
import { TaskModel } from '@/models/taskModel';
import { teamDataModel } from '@/models/team/teamData.model';
import { teamDialogModel } from '@/models/team/teamDialog.model';
import {
    type SignInSelectProductView,
    type SignInSelectProductListView,
} from '@/services/open-api-docs/home/<USER>/schemas';
import { reportKeyActionStart, reportKeyActionEnd } from '@/utils/log/keyActionLog';

import { useSelectProductApiModel } from './api/selectProductApiModel';
import { useAudioModel, SoundType } from './audioModel';
import { useCalendarModel } from './calendarModel';
import { useForwardRushGridModel } from './forwardRushGridModel';
import { useHomeModel } from './homeModel';
import { TaskType, CUSTOM_POPUP_TYPE } from './popup.model';
import { useSnapShotModel } from './snapShotModel';

export const useSelectProductModel = createUseModel(({ getModelInstance }) => {
    const { refreshHome, sinInHomeInfo, isNoStep } = useHomeModel();
    const { productListData, selectProductMutation, getSelectProductData } = useSelectProductApiModel();
    const { isProgressSignedAnim } = useSnapShotModel();
    const { refreshCalendar } = useCalendarModel();
    const { kconfConfig } = useConfigModel();
    const { playSound } = useAudioModel();
    const { log } = useCaptureDebugLog('selectProduct');
    const replaceProductConfirmText = computed(() => kconfConfig.value?.replaceProductConfirmText);
    /* 换品弹窗标题 */
    const selectProductSheetTitle = computed(() => productListData.value?.title ?? '');
    /* 换品弹窗副标题 */
    const selectProductSheetSubTitle = computed(() => productListData.value?.subTitle ?? '');
    /* 换品弹窗商品列表 */
    const selectProductList = computed(() => productListData.value?.productList ?? []);
    const firstFreeChangeProduct = ref(false);
    /* 换品动效信息 */
    const choiceProductAnim = reactive({
        play: false,
        afterUrl: '',
    });
    const { currentGuide } = useGuideState();
    const { openPopup, currentTasks, queue } = useTaskQueueModel();
    const { afterSelect } = useForwardRushGridModel();
    const { sendClick } = useLogger();
    // 第一次选品时记录选品id
    const firstSelectedProductId = ref();
    // 进度条动效控制
    const setChoiceProductAnim = (show: boolean, afterUrl?: string) => {
        if (show) {
            // TIPS: 选品反馈动效 与 打卡成功动效冲突, 因此在选品反馈动效前, 移除打卡成功动效相关 class
            isProgressSignedAnim.value = false;
            choiceProductAnim.afterUrl = afterUrl ?? '';
            choiceProductAnim.play = true;
        } else {
            // 换品动效重置为默认值, 支持连续换品
            choiceProductAnim.play = false;
        }
    };

    const getReplaceProductDesc = () => {
        if (firstFreeChangeProduct.value) {
            return replaceProductConfirmText.value?.FIRST ?? DefaultReplaceProductConfirmText.FIRST;
        }
        if (isNoStep.value) {
            return replaceProductConfirmText.value?.NORMAL2 ?? DefaultReplaceProductConfirmText.NORMAL2;
        }
        return replaceProductConfirmText.value?.NORMAL ?? DefaultReplaceProductConfirmText.NORMAL;
    };

    const selectProduct = async (
        product: SignInSelectProductView,
        logParams: { source: string; button_name: string; good_id: number },
        close?: () => void,
        // eslint-disable-next-line sonarjs/cognitive-complexity
    ) => {
        // 商品数量不足。不可选
        if (product?.productRemainStatus === 1) {
            toast(TOAST_LIMIT_PRODUCT);
            return;
        }
        if (product?.productId === (firstSelectedProductId.value ?? productListData.value?.selectedProductId)) {
            toast(TOAST_DEFAULT_PRODUCT);
            close?.();
            afterSelect.value = true;
            return;
        }
        const handleSelectProduct = async () => {
            log('handleSelectProduct--请求换品');
            await selectProductMutation.mutate({
                productId: product.productId!,
                templateId: product.templateId!,
            });
            if (selectProductMutation.info.error) {
                console.log('【handleSelectProduct】换品失败');
                log('handleSelectProduct--换品失败', selectProductMutation.info.error);
                await toast(selectProductMutation.info.error?.data?.error_msg ?? '网络错误');
                return false;
            } else {
                log('handleSelectProduct--换品成功');
                console.log('【handleSelectProduct】换品成功');
                firstSelectedProductId.value = undefined;
                firstFreeChangeProduct.value = false;
                // eslint-disable-next-line no-underscore-dangle
                currentGuide.value?.el?.__destroyGuide?.();
                refreshHome().then(() => {
                    // 换品成功后重新写入日历
                    log('handleSelectProduct--换品成功后重新写入日历');
                    reportKeyActionEnd({
                        name: `sign_in_select_product`,
                    });
                    // 换品成功后刷新任务列表
                    getModelInstance(TaskModel)?.tasksRefetch();
                    refreshCalendar();
                });

                return true;
            }
        };
        // 关闭选品弹窗
        close?.();
        // 非首次免费换品且组队成功时，需要先退队
        if (!firstFreeChangeProduct.value && getModelInstance(teamDataModel)?.exitTeamGuide.value) {
            // 组队面板二次确认弹窗
            openPopup({
                component: () => import('@/components/common-modals/CommonModal.vue'),
                data: {
                    subTitle: '更换商品需先退队',
                    desc: replaceProductConfirmText.value?.TEAM ?? DefaultReplaceProductConfirmText.TEAM,
                    icon: product.productIcon ?? '',
                    mainButton: '去退队',
                    subButton: '不换了',
                    type: 'dialog',
                    lightType: 'none',
                    btnClick: async (popup) => {
                        const { position } = popup;
                        if (position === 'mainClick') {
                            getModelInstance(teamDialogModel)?.openTeamPanel();
                        }
                        popup?.destroy();
                    },
                },
                options: {
                    name: TaskType.ACTIVE_POPUP,
                    ext: {
                        noPlayShowSound: true,
                    },
                },
            });
        } else {
            const mainButtonText = firstFreeChangeProduct.value ? '就拿它' : '确定更换';
            const notFlyToTarget = ref(false);
            const btnLoading = ref(false);
            setChoiceProductAnim(false);
            afterSelect.value = false;

            // 换品二次确认弹窗
            openPopup({
                component: () => import('@/components/common-modals/CommonModal.vue'),
                data: {
                    subTitle: firstFreeChangeProduct.value ? '确定白拿这个吗？' : '更换后进度会清零',
                    desc: getReplaceProductDesc(),
                    icon: product.productIcon ?? '',
                    mainButton: mainButtonText,
                    subButton: '再想想',
                    type: 'dialog',
                    cloneFlyToTarget: 'progress-view',
                    clonePilotProps: {
                        duration: 467,
                        bezier: [0, 0, -47, -84.5],
                    },
                    notFlyToTarget,
                    lightType: 'none',
                    btnLoading,
                    btnClick: async (popup) => {
                        const { position } = popup;
                        playSound(SoundType.FLY_TO_PRODUCT);
                        if (position === 'mainClick') {
                            log('换品二次确认弹窗--主按钮点击', logParams);
                            btnLoading.value = true;
                            const resSelect = await handleSelectProduct();
                            sendClick('OP_ACTIVITY_REWARD_CHOOSE_RESULT', {
                                source: logParams.source,
                                good_id: logParams.good_id,
                                button_name: mainButtonText,
                                result_type: resSelect ? 'success' : 'fail',
                            });
                            btnLoading.value = false;

                            if (resSelect) {
                                setChoiceProductAnim(true, product.productIcon ?? '');
                                toast(SELECT_PRODUCT_SUCCESS);
                                popup?.destroy();
                            } else {
                                notFlyToTarget.value = true;
                                popup?.destroy();
                            }
                            afterSelect.value = true;
                        } else if (position === 'subClick') {
                            log('换品二次确认弹窗--副按钮点击', logParams);
                            notFlyToTarget.value = true;
                            afterSelect.value = false;
                            popup?.destroy();
                            // eslint-disable-next-line @typescript-eslint/no-use-before-define
                            const selectRes = await openSelectProductSheet({ logSource: 'choose' });
                            if (!selectRes || selectRes === 'close') {
                                afterSelect.value = true;
                            }
                            sendClick('OP_ACTIVITY_REWARD_CHOOSE_RESULT', {
                                source: logParams.source,
                                good_id: logParams.good_id,
                                button_name: '再想想',
                            });
                        } else {
                            afterSelect.value = true;
                            log('换品二次确认弹窗--其他按钮点击', logParams);
                            notFlyToTarget.value = true;
                            popup?.destroy();
                        }
                    },
                },
                options: {
                    name: TaskType.ACTIVE_POPUP,
                    ext: {
                        noPlayShowSound: true,
                    },
                },
            });
        }
    };
    const handlePickItem = async (
        v?: SignInSelectProductView,
        index?: string,
        btnName?: string,
        logSource?: string,
        close?: () => void,
    ) => {
        if (v) {
            const logParams = {
                source: logSource ?? '',
                button_name: btnName ?? '',
                good_id: v?.productId!,
            };
            sendClick('OP_ACTIVITY_REWARD_CHOOSE_POP', logParams);
            log('handlePickItem--点击商品', logParams);
            reportKeyActionStart({
                name: `sign_in_select_product`,
            });
            selectProduct(v, logParams, close);
        } else {
            console.log('【handlePickItem】无商品信息');
        }
    };
    // 打开换品弹窗
    const openSelectProductSheet = async ({
        popup,
        topTitle,
        logSource,
    }: {
        popup?: SignInSelectProductListView;
        topTitle?: string;
        logSource?: string;
    }) => {
        if (popup) {
            firstFreeChangeProduct.value = popup?.freeChange ?? false;
            firstSelectedProductId.value = popup?.selectedProductId;
            log('换品弹窗--后端首日下发', popup);
            // 首日选品触发,不调用接口
            const task = openPopup({
                component: () => import('@/components/popups/select-product-sheet/SelectProductSheet.vue'),
                data: {
                    popupType: CUSTOM_POPUP_TYPE.SELECT_PRODUCT_POPUP,
                    title: popup?.title ?? '',
                    subTitle: popup?.subTitle ?? '',
                    topTitle: topTitle ?? '',
                    firstSelectTag: popup?.firstSelectTag ?? '',
                    productList: popup?.productList ?? [],
                    finalProduct: sinInHomeInfo?.value?.product!,
                    handlePickItem,
                    setChoiceProductAnim,
                    logSource,
                },
                options: {
                    name: TaskType.ACTIVE_POPUP,
                    ext: {
                        noPlayShowSound: true,
                    },
                },
            });
            return task.end;
        } else {
            const res = await getSelectProductData();
            firstFreeChangeProduct.value = res?.freeChange ?? false;
            log('换品弹窗--手动触发', res);
            if (res) {
                const task = openPopup({
                    component: () => import('@/components/popups/select-product-sheet/SelectProductSheet.vue'),
                    data: {
                        popupType: CUSTOM_POPUP_TYPE.SELECT_PRODUCT_POPUP,
                        title: selectProductSheetTitle.value,
                        subTitle: selectProductSheetSubTitle.value,
                        firstSelectTag: productListData.value?.firstSelectTag ?? '',
                        productList: selectProductList.value,
                        finalProduct: sinInHomeInfo?.value?.product!,
                        handlePickItem,
                        setChoiceProductAnim,
                        logSource,
                    },
                    options: {
                        name: TaskType.ACTIVE_POPUP,
                    },
                });
                return task.end;
            }
            return;
        }
    };
    return {
        selectProductSheetTitle,
        selectProductSheetSubTitle,
        selectProductList,
        selectProduct,
        handlePickItem,
        openSelectProductSheet,
        choiceProductAnim,
    };
});
