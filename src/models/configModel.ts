import { createModel, useModel } from '@gundam/model';
import type { GuideConfigOptions } from '@pet/25cny.guide-directive/type';

import { useForwardStationModel } from './forwardStationModel';
import { useHomeModel } from './homeModel';

/**
 * ConfigModel
 * 在 Mock 和 kconf 中配置后，记得在这里TS类型加上
 */
export const configModel = createModel(() => {
    const { homeData } = useHomeModel();
    const { gCurrentStation } = useForwardStationModel();

    const kconfConfig = computed<KconfFEConfig>(() => homeData.value?.homeFEConstantsConfig);
    const kconfUEConfig = computed<KconfUEConfig>(() => homeData.value?.homeUEConstantsConfig);

    /** 主题key */
    const themeKey = computed(() => (gCurrentStation.value?.stationInfo.stationThemeKey as ThemeKey) ?? 'default');
    // const themeKey = computed(() => 'furao');

    const weakGuideConfig = computed(() => kconfConfig.value?.weakGuideConfig);

    return {
        /** FE Config */
        kconfConfig,
        /** UE Config */
        kconfUEConfig,
        /** 主题key */
        themeKey,
        /** 引导配置 */
        weakGuideConfig,
        /** 页面配置 */
        themeConfig: computed(
            () =>
                kconfConfig.value?.frameConfig?.themeMap?.[themeKey.value] ??
                kconfConfig.value?.frameConfig?.themeMap.default,
        ),
        sharePoster: computed(
            () => kconfUEConfig.value?.['sharePoster_' + themeKey.value] ?? kconfUEConfig.value.sharePoster_default,
        ),
        /** 挽留弹窗配置 */
        retainFrequency: computed(() => kconfConfig.value?.retainFrequency),
        /** inpush配置 */
        inpushConfig: computed(() => kconfConfig.value?.inpushConfig),
    };
});

export const useConfigModel = () => useModel(configModel);

export type IMainButtonConfig = Record<ForwardRushBtnStatus, ForwardRushBtnInfo> & {
    leftSideButtonText: string;
    rightSideButtonText: string;
    noStepToast: string;
};
interface ReplaceProductConfirmTextKeys {
    FIRST: string;
    NORMAL: string;
    NORMAL2: string;
    TEAM: string;
}

export type ThemeKey = 'default' | 'jinghua' | 'xile' | 'furao' | 'fengshou' | 'jiangnan' | 'yangfan' | 'duocai';

export interface KconfFEConfig {
    // 页面设置
    frameConfig: {
        // 键为地图的key
        themeMap: Record<
            ThemeKey,
            {
                // 主题色，状态栏颜色
                themeColor: string;
                // 头部背景遮罩主颜色，第一个颜色需要和主题色一致，不然会有断层
                headerBackMaskBackground: string;
                // 底部背景遮罩颜色
                footerBackMaskBackground: string;
                // 背景颜色
                backgroundColor: string;
            }
        >; // 主题色配置
    };
    /** 花字文案配置 */
    flowerTip: {
        signed: string; // 打卡成功
        day2: string; // 第二天：送你一步 冲!
        day100: string; // 第100天最后一步
    };
    /** 新手引导标题 */
    guideTitle: string;
    /** 主按钮的文案配置 */
    mainButtonConfig: IMainButtonConfig;
    /** 桌面图标弹窗配置 */
    shortcutConfig: {
        name: string; // 百日心愿
        id: string; // summer25
        // 中间页配置
        bgColor: string; // '#F2FCFF'
        btText: string; // '打开活动页'
    };
    /** 挽留弹窗频控 */
    retainFrequency?: {
        /** 打卡挽留弹窗 */
        signRetainPopup: {
            dayTime: number;
            allTime: number;
        };
        /** 快捷方式挽留弹窗 */
        shortcutRetainPopup: {
            dayTime: number;
            allTime: number;
        };
        /** 搜索挽留弹窗 */
        searchRetainPopup: {
            dayTime: number;
            allTime: number;
        };
    };
    inpushConfig?: {
        // 兜底轮询间隔
        interval: number;
    };

    /** 弱引导相关配置 */
    weakGuideConfig?: {
        /** 站点奖励气泡 */
        GUIDE_REWARD: GuideConfigOptions;
        /** 引导赚步数 */
        GUIDE_RIGHT_BUTTON: GuideConfigOptions;
        /** 引导组队 */
        GUIDE_TEAM: GuideConfigOptions;
        /** 引导组队打卡 */
        GUIDE_CARD: GuideConfigOptions;
        /** 左侧任务面板小手频控 */
        GUIDE_LEFT_TASK?: GuideConfigOptions;
        /** 右侧任务面板小手频控 */
        GUIDE_RIGHT_TASK?: GuideConfigOptions;
        /** 主按钮小手引导 */
        GUIDE_MAIN_BUTTON?: GuideConfigOptions;
    };
    /* 换品二期确认弹窗副标题 */
    replaceProductConfirmText?: ReplaceProductConfirmTextKeys;
    /* 播报区角标文案 */
    broadcastTag?: {
        INTERCEPT: string;
        UN_SIGN: string;
        SIGNED: string;
    };
    /* 播报区文案 */
    broadcastText?: {
        INTERCEPT: string;
        DEFAULT: string;
    };
    /** 进度条 icon 文案 */
    progressPointText: {
        INTERCEPT: string;
        DEFAULT: string;
        TODAY: string;
        TARGET: string;
    };
    /* 领奖过渡页文案 */
    rewardTransPopupText?: {
        title: string;
        subTitle: string;
    };
    /* 主按钮小手引导显示天数配置 */
    mainButtonGuideHandShowDays: number;
    // 新 UI 进度卡片文案配置
    progressViewConf?: {
        topText: {
            default: string;
            signed: string;
            intercept: string;
        };
    };
}

export interface KconfUEConfig extends Record<string, string> {
    /** 会场主链接 */
    activityUrl: string;
    // searchContentIcon:
    //     'https://kcdn-w1.staging.kuaishou.com/kc/files/a/cny2025-server/warmup/home/<USER>/searchContentIcon.png',

    // mainTitleImg:
    //     'https://kcdn-w1.staging.kuaishou.com/kc/files/a/cny2025-server/warmup/home/<USER>/main-title.0b522789a6414813.png',

    /** 活动规则页链接 */
    activityRuleUrl: string;
    /** 钱包页链接 */
    walletUrl: string;
    /** 客服页面链接 */
    customerServiceUrl: string;

    /** 桌面图标图片 */
    shortcutIcon: string;
    /** 活动链接，桌面图标跳转需要 */
    shortcutUrl: string;
    /** 添加桌面图标弹窗里面展示的图片 */
    shortcutContentIcon: string;
    /** 桌面图标中间页背景图 */
    shortcutBgImg: string;

    /** 分享打卡天数 数字图片 */
    shareNumber0: string;
    shareNumber1: string;
    shareNumber2: string;
    shareNumber3: string;
    shareNumber4: string;
    shareNumber5: string;
    shareNumber6: string;
    shareNumber7: string;
    shareNumber8: string;
    shareNumber9: string;

    /** 和主题对应的分享海报，但是还不确定主题有多少个，主题名等 */
    /** 分享海报，对应 default */
    sharePoster_default: string;
    /** 分享海报，对应 jinghua */
    sharePoster_jinghua: string;
    /** 分享海报，对应 xile */
    sharePoster_xile: string;
    /** 分享海报，对应 furao */
    sharePoster_furao: string;
}

/**
 * 主按钮的状态
 * 按照优先级排列：断签>已打卡>免任务卡>标准态
 */
export enum ForwardRushBtnStatus {
    SIGN_INTERCEPTED = 'SIGN_INTERCEPTED', // 断签
    SIGNED = 'SIGNED', // 已打卡
    SIGN_FREE = 'SIGN_FREE', // 免任务卡
    UN_SIGN = 'UN_SIGN', // 未签到
    FIRST_DAY = 'FIRST_DAY', // 首日
    OTHER = 'OTHER', // 其他
}

export interface ForwardRushBtnInfo {
    buttonText: string; // 主按钮文案
    newButtonText: string; // 新主按钮文案--0611的变更，已完成打卡的情况下需要展示保持“向前冲”
    buttonSubText?: string; // 主按钮副文案
    bubbleText?: string; // 气泡第一行文案
    bubbleSubText?: string; // 气泡第二行文案
    duration?: number; // 气泡展示时长
}
