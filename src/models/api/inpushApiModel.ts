import { createUseModel } from '@gundam/model';
import { useRestQueryWithType } from '@gundam/model/utils/apiConfig';
import { injectVisibility, useDynamicInterval } from '@pet/yau.yoda';
import { until, whenever } from '@vueuse/core';

import { pollingControllerInpush } from '@/services/open-api-docs/home/<USER>/vue-apollo-model-client';
import { InpushType } from '@/services/open-api-docs/home/<USER>/schemas';
import { reportKeyActionEnd } from '@/utils/log/keyActionLog';

import { useHomeApiModel } from './homeApiModel';
import { taskApiModel } from './taskApiModel';
import { useCalendarModel } from '../calendarModel';
import { useConfigModel } from '../configModel';
// import { forwardRushBtnModel } from '../forwardRushBtnModel';
// import { gridTaskSheetModel } from '../gridTaskSheetModel';
import { TaskType, usePopupModel } from '../popup.model';
import { teamActionModel } from '../team/teamAction.model';

// 不需要展示 inpush 的类型
const excludeInpushTypes = [InpushType.GRID_TASK_UPDATE];

/**
 * inpushApiModel 轮询
 * 除夕方案：在 refreshPush 中加判断，轮询不会暂停，但是条件不足会不发请求，业务不用管
 * 预热方案：导出暂停方法，业务来控制轮询（暂定看看需不需要）
 */
export const useInpushApiModel = createUseModel(({ getModelInstance }) => {
    const pageVisible = injectVisibility()?.visible ?? ref(true);
    const { openInpush, currentTasks } = usePopupModel();
    const { data: homeData, refreshHome, forceRefreshHome } = useHomeApiModel(); // inpushConfig
    const { inpushConfig } = useConfigModel();
    const { refreshCalendar } = useCalendarModel();

    const { refetch, info } = useRestQueryWithType(pollingControllerInpush)({ skip: () => !homeData.value });

    const nextTimeMills = computed(() => info.data?.nextTimeMills ?? inpushConfig.value?.interval ?? 60 * 1000);

    const {
        pause: rawPause,
        resume: rawResume,
        trigger: triggerInpush,
    } = useDynamicInterval(
        async () => {
            if (!pageVisible.value || info.loading || !homeData.value) {
                return;
            }
            await refetch();
        },
        {
            interval: nextTimeMills,
            // 切前台重新请求， 后台状态不会进行轮询
            refreshAfterVisible: false,
        },
    );

    let pauseStack = 0;
    const pausePolling = () => {
        if (pauseStack === 0) {
            rawPause();
        }
        pauseStack++;
    };
    const resumePolling = () => {
        pauseStack--;
        if (pauseStack === 0) {
            rawResume();
        }
    };

    const refresh = async () => {
        await until(() => info.loading).toBe(false);
        pausePolling();
        await refetch();
        resumePolling();
    };

    // 轮询数据变化
    watch(
        () => info.data,
        (data) => {
            // 刷新主接口
            data?.needRefreshPopup && refreshHome();
            // 弹inpush
            data?.inpushList.forEach((inpush) => {
                if (!excludeInpushTypes.includes(inpush.inpushType)) {
                    openInpush(inpush);
                }
                // 续签任务完成后，关闭续签弹窗
                if (inpush.inpushType === 'RESUME_SUCCESS') {
                    currentTasks.value?.forEach((task) => {
                        task.name === TaskType.HUGE_SIGN_IN_RESUME && task.triggerDestroy();
                    });
                    refreshHome().then(() => {
                        reportKeyActionEnd({
                            name: `sign_in_resume`,
                        });
                        // 完成续签后删除并重新写入日历
                        refreshCalendar();
                        getModelInstance(taskApiModel)?.tasksRefetch();
                    });
                }
                if (inpush.inpushType === InpushType.ASSIST) {
                    // 好友助力 通过inpush通知 刷新任务接口获取最新数据
                    getModelInstance(taskApiModel)?.tasksRefetch();
                }

                if (inpush.inpushType === InpushType.TEAM) {
                    getModelInstance(teamActionModel)?.teamPanelMutate?.();
                    getModelInstance(taskApiModel)?.tasksRefetch();
                }

                if (inpush.inpushType === InpushType.GRID_TASK_UPDATE) {
                    // 格子任务更新（看广告、双端互拉），刷新主接口（更新格子任务进度，不拉 popList）
                    refreshHome('LUCK_SHAKE_SUDOKU');
                }
            });
        },
    );

    // 页面可见时，立即触发一次轮询
    whenever(pageVisible, () => {
        triggerInpush();
    });

    return {
        data: info.data,
        refreshInpush: refresh,
        pausePolling,
        resumePolling,
    };
});
