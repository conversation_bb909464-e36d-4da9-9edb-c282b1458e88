/**
 * apng resource data
 * generate@ 1 for @vision/runtime@^0.7.0
 * preview@ https://vision.corp.kuaishou.com/effect/8822
 *
 * wrapper size expected: { width: 150px; height: 170px; }
 */
import '@vision/runtime/adapter/apng';

import entrySrc from './assets/effect.png';
import entrySrcWebp from './assets/effect.webp';
import entrySrcAvif from './assets/effect.avif';

export default Object.seal({
    type: 'apng' as const,

    meta: {
        version: 1,
        id: '8822',
        effectVersion: 0,
    },

    layout: {
        adaptive: 414,
    },

    options: {
        /**
         * 默认循环播放
         */
        loop: false,
        /**
         * 默认自动播放
         */
        autoplay: true,
        ratio: 2,
    },

    entrySrc,

    extraData: {
        /**
         * 是否使用 apng-player 播放。
         * 如果没有特别需要，默认使用 img 播放
         */
        enableHandler: false,

        /**
         * 是否支持转换成webp/avif格式播放。
         */
        convertible: true,

        /**
         * 是否支持转换成webp/avif格式播放。
         */
        entrySrcAvif,
        entrySrcWebp,
    },
});
