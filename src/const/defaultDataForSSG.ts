import { ref } from 'vue';

import type { KconfFEConfig } from '@/models/configModel';
import { type HomeView } from '@/services/open-api-docs/home/<USER>/schemas';

export const homeFEConstantsConfig: { frameConfig: KconfFEConfig['frameConfig'] } = {
    // 页面设置
    frameConfig: {
        themeMap: {
            // 兜底主题，应对key没对应上的情况
            default: {
                // 主题色，状态栏颜色
                themeColor: '#f85c3e',
                // 头部背景遮罩主颜色，第一个颜色需要和主题色一致，不然会有断层
                headerBackMaskBackground: 'linear-gradient(180deg, #f85c3e 0%, #ff7840 20%, #fba169 62%, #fff0 100%)',
                // 底部背景遮罩颜色
                footerBackMaskBackground: 'linear-gradient(180deg, #fff0 0%, #ffdcbfe6 42%, #ffefd1 100%)',
                backgroundColor: '#FFDCBFE5',
            },
            jinghua: {
                themeColor: '#f85c3e',
                headerBackMaskBackground: 'linear-gradient(180deg, #f85c3e 0%, #ff7840 20%, #fba169 62%, #fff0 100%)',
                footerBackMaskBackground: 'linear-gradient(180deg, #fff0 0%, #ffdcbfe6 42%, #ffefd1 100%)',
                backgroundColor: '#FFDCBFE5',
            },
            xile: {
                themeColor: '#188BFF',
                headerBackMaskBackground: 'linear-gradient(180deg, #188BFF 0%, #57A7F7 20%, #98CBFF 62%, #fff0 100%)',
                footerBackMaskBackground: 'linear-gradient(180deg, #fff0 0%, #d4eaffe6 42%, #C7E4FF 100%)',
                backgroundColor: '#D4EAFFE5',
            },
            furao: {
                themeColor: '#BBC527',
                headerBackMaskBackground: 'linear-gradient(180deg, #BBC527 0%, #CFD83E 20%, #E2E796 67%, #fff0 100%)',
                footerBackMaskBackground: 'linear-gradient(180deg, #fff0 0%, #fdf9c8e6 42%, #EAE497 100%)',
                backgroundColor: '#EAE497',
            },
            fengshou: {
                themeColor: '#FFB126',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #FFB126 -7.01%, #FFBF4E 19.83%, #FFCF7A 67.82%, rgba(255, 207, 122, 0) 99.42%)',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(255, 223, 166, 0) 0.78%, rgba(255, 223, 166, 0.9) 42.63%, #FFDFA6 100%)',
                backgroundColor: '#FFDFA6',
            },
            jiangnan: {
                backgroundColor: '#BBFFF4',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(187, 255, 244, 0) 0.78%, rgba(187, 255, 244, 0.9) 42.63%, #BBFFF4 100%)',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #20C1C3 -7.01%, #08D5C4 19.83%, #08D5C4 67.82%, rgba(8, 213, 196, 0) 99.42%)',
                themeColor: '#BBFFF4',
            },
            yangfan: {
                backgroundColor: '#8EF1FF',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(142, 241, 255, 0) 0.78%, rgba(142, 241, 255, 0.9) 42.63%, #8EF1FF 100%)',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #00CBFC -7.01%, #00CBFC 19.83%, #00CBFC 67.82%, rgba(0, 203, 252, 0) 99.42%)',
                themeColor: '#00CBFC',
            },
            duocai: {
                backgroundColor: '#CCF1A9',
                footerBackMaskBackground:
                    'linear-gradient(180deg, rgba(204, 241, 169, 0) 0.78%, rgba(204, 241, 169, 0.9) 42.63%, #CCF1A9 100%)',
                headerBackMaskBackground:
                    'linear-gradient(179.33deg, #55DA56 -7.01%, #42D443 19.83%, #85E686 67.82%, rgba(130, 220, 131, 0) 99.42%)',
                themeColor: '#55DA56',
            },
        },
    },
};

export const RawDataDefault = ref<HomeView>({
    todaySigned: false,
    chessboard: {
        progress: {
            currentStep: 0,
            expectTotalStep: 1,
            signed: false,
        },
        initStationCount: 5,
    },
    mainButton: {},
    titleInfo: {},
    accountModel: {},
    popList: [],
    needSig3Path: [],
});
